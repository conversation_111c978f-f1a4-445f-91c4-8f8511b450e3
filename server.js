require('dotenv').config();
const express = require('express');
const path = require('path');
const cors = require('cors');
const morgan = require('morgan');
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
require('winston-daily-rotate-file');
const mongoose = require('mongoose');
const Redis = require('ioredis');
const { Server } = require('socket.io');
const http = require('http');
const Bull = require('bull');
const { connect } = require('./db/mongo');
const config = require('./config');
const { calculateIndicators } = require('./utils/indicators');
const { generateSignals } = require('./utils/signals');
const { validateInputs } = require('./utils/validation');
const cache = require('./cache');

// Configure Redis
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  retryStrategy: (times) => Math.min(times * 50, 2000)
});

// Configure Bull job queues
const marketDataQueue = new Bull('market data processing', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  }
});

const signalQueue = new Bull('signal generation', {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379
  }
});

// Job processors
marketDataQueue.process('fetch-market-data', async (job) => {
  const { symbol, timeframe } = job.data;
  logger.info(`Processing market data job for ${symbol}:${timeframe}`);

  try {
    // Fetch market data (implement your API calls here)
    const marketData = await fetchMarketDataFromAPI(symbol, timeframe);

    // Store in database
    if (marketData && marketData.length > 0) {
      await mongoose.connection.db.collection('marketData').insertMany(
        marketData.map(item => ({
          ...item,
          symbol: symbol.toUpperCase(),
          timeframe,
          timestamp: new Date(item.timestamp || Date.now()),
          createdAt: new Date()
        }))
      );

      // Broadcast update via Socket.IO
      if (global.broadcastMarketUpdate) {
        global.broadcastMarketUpdate(symbol, marketData);
      }
    }

    return { success: true, count: marketData?.length || 0 };
  } catch (error) {
    logger.error(`Market data job failed for ${symbol}:${timeframe}`, error);
    throw error;
  }
});

signalQueue.process('generate-signals', async (job) => {
  const { symbol, timeframe } = job.data;
  logger.info(`Processing signal generation job for ${symbol}:${timeframe}`);

  try {
    // Fetch recent market data
    const marketData = await mongoose.connection.db.collection('marketData').find({
      symbol: symbol.toUpperCase(),
      timeframe
    })
    .sort({ timestamp: -1 })
    .limit(100)
    .toArray();

    if (marketData && marketData.length > 0) {
      // Generate signals
      const signals = generateSignals(marketData);

      // Store signals
      if (signals && signals.length > 0) {
        await mongoose.connection.db.collection('signals').insertMany(
          signals.map(signal => ({
            ...signal,
            symbol: symbol.toUpperCase(),
            timeframe,
            createdAt: new Date()
          }))
        );

        // Broadcast signals via Socket.IO
        if (global.broadcastSignal) {
          signals.forEach(signal => {
            global.broadcastSignal(symbol, signal);
          });
        }
      }
    }

    return { success: true, signalCount: signals?.length || 0 };
  } catch (error) {
    logger.error(`Signal generation job failed for ${symbol}:${timeframe}`, error);
    throw error;
  }
});

// Placeholder function for API calls (implement with your actual API)
async function fetchMarketDataFromAPI(symbol, timeframe) {
  // This would integrate with your actual market data APIs
  // For now, return empty array
  logger.warn(`fetchMarketDataFromAPI not implemented for ${symbol}:${timeframe}`);
  return [];
}

// Configure Winston logger with daily rotate
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxFiles: '14d'
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxFiles: '14d'
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

const app = express();
const PORT = process.env.PORT || 3000;

// Create HTTP server for Socket.IO
const server = http.createServer(app);
const io = new Server(server, {
  cors: {
    origin: process.env.NODE_ENV === 'production' ? false : ["http://localhost:3000", "http://127.0.0.1:3000"],
    methods: ["GET", "POST"]
  }
});

// Setup security and performance middleware with configurations from config file
app.use(helmet(config.APP_SETTINGS.security.helmet));
app.use(compression()); // Compress responses
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Configure CORS using settings from config
app.use(cors({
  origin: config.APP_SETTINGS.security.cors.allowedOrigins,
  methods: config.APP_SETTINGS.security.cors.allowedMethods,
  allowedHeaders: config.APP_SETTINGS.security.cors.allowedHeaders,
  credentials: true,
  maxAge: config.APP_SETTINGS.security.cors.maxAge
}));

// Set up request logging
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.http(message.trim())
  }
}));

// Serve static files
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

// Add rate limiting for all requests using config
const apiLimiter = rateLimit({
  windowMs: config.APP_SETTINGS.rateLimiting.windowMs,
  max: config.APP_SETTINGS.rateLimiting.maxRequestsPerWindow,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many requests, please try again later.'
  }
});
app.use('/api/', apiLimiter);

// Graceful MongoDB connection with exponential backoff
const connectWithRetry = async (retries = 5, initialDelay = 1000) => {
  for (let i = 0; i < retries; i++) {
    try {
      await connect();
      logger.info('Successfully connected to MongoDB');
      return;
    } catch (err) {
      const delay = initialDelay * Math.pow(2, i);
      logger.error(`MongoDB connection attempt ${i + 1}/${retries} failed:`, err);
      if (i < retries - 1) await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw new Error('Failed to connect to MongoDB after multiple attempts');
};

// Initialize services
const initializeServices = async () => {
  try {
    await connectWithRetry();
    await redis.ping();
    logger.info('Redis connection established');
  } catch (err) {
    logger.error('Service initialization failed:', err);
    process.exit(1);
  }
};

initializeServices();

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbStatus = await mongoose.connection.db.admin().ping();
    const redisStatus = await redis.ping();

    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbStatus ? 'connected' : 'disconnected',
      cache: redisStatus === 'PONG' ? 'connected' : 'disconnected',
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version
    });
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Health check failed'
    });
  }
});

// Import authentication middleware and services
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      status: 'error',
      message: 'Access token required'
    });
  }

  jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key', (err, user) => {
    if (err) {
      return res.status(403).json({
        status: 'error',
        message: 'Invalid or expired token'
      });
    }
    req.user = user;
    next();
  });
};

// API Routes
const apiRouter = express.Router();

// Authentication Routes
apiRouter.post('/auth/register', async (req, res) => {
  try {
    const { email, password, username, fullName } = req.body;

    // Validate input
    if (!email || !password || !username) {
      return res.status(400).json({
        status: 'error',
        message: 'Email, password, and username are required'
      });
    }

    // Check if user already exists
    const existingUser = await mongoose.connection.db.collection('users').findOne({
      $or: [{ email }, { username }]
    });

    if (existingUser) {
      return res.status(400).json({
        status: 'error',
        message: 'User already exists with this email or username'
      });
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const newUser = {
      email: email.toLowerCase(),
      username,
      password: hashedPassword,
      fullName: fullName || username,
      role: 'user',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await mongoose.connection.db.collection('users').insertOne(newUser);

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: result.insertedId,
        email: newUser.email,
        username: newUser.username,
        role: newUser.role
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Return user data without password
    const { password: _, ...userWithoutPassword } = newUser;

    res.status(201).json({
      status: 'success',
      data: {
        user: { ...userWithoutPassword, _id: result.insertedId },
        token
      }
    });

  } catch (error) {
    logger.error('Registration error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Registration failed'
    });
  }
});

apiRouter.post('/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate input
    if (!email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Email and password are required'
      });
    }

    // Find user
    const user = await mongoose.connection.db.collection('users').findOne({
      email: email.toLowerCase()
    });

    if (!user) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Update last login
    await mongoose.connection.db.collection('users').updateOne(
      { _id: user._id },
      { $set: { lastLogin: new Date() } }
    );

    // Generate JWT token
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        username: user.username,
        role: user.role
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '24h' }
    );

    // Return user data without password
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      status: 'success',
      data: {
        user: userWithoutPassword,
        token
      }
    });

  } catch (error) {
    logger.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed'
    });
  }
});

apiRouter.get('/auth/me', authenticateToken, async (req, res) => {
  try {
    const user = await mongoose.connection.db.collection('users').findOne(
      { _id: req.user.userId },
      { projection: { password: 0 } }
    );

    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    res.json({
      status: 'success',
      data: { user }
    });

  } catch (error) {
    logger.error('Get user error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user data'
    });
  }
});

// Market Data endpoint - with caching
apiRouter.get('/market/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  const limit = parseInt(req.query.limit || '100', 10);

  try {
    // Use cache wrapper to automatically handle cache read/write
    const cacheKey = `market_${symbol}_${timeframe}_${limit}`;
    const cacheTTL = config.CACHE_TTL_BY_TIMEFRAME[timeframe] || config.CACHE_SETTINGS.redis.defaultTTL;

    const data = await cache.cacheWrapper(
      cacheKey,
      async () => {
        // Fetch from database if not in cache
        const marketData = await mongoose.connection.db.collection('marketData').find({
          symbol: symbol.toUpperCase(),
          timeframe
        })
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();

        return marketData;
      },
      cacheTTL
    );

    res.json({
      status: 'success',
      data
    });
  } catch (error) {
    logger.error(`Error fetching market data: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch market data'
    });
  }
});

// Generate indicators endpoint
apiRouter.get('/indicators/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  const { indicators: requestedIndicators } = req.query;

  try {
    // Fetch market data
    const marketData = await mongoose.connection.db.collection('marketData').find({
      symbol: symbol.toUpperCase(),
      timeframe
    })
    .sort({ timestamp: -1 })
    .limit(config.APP_SETTINGS.maxDataPoints)
    .toArray();

    if (!marketData || marketData.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'No market data found for the specified symbol and timeframe'
      });
    }

    // Process indicators
    const result = calculateIndicators(marketData, requestedIndicators);

    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error(`Error calculating indicators: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to calculate indicators'
    });
  }
});

// Generate trading signals endpoint
apiRouter.get('/signals/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;

  try {
    // Use cache for signals with a shorter TTL than market data
    const cacheKey = `signals_${symbol}_${timeframe}`;
    const cacheTTL = Math.min(config.CACHE_TTL_BY_TIMEFRAME[timeframe] / 2 || 1800, 1800); // Half of market data TTL or 30 min

    const result = await cache.cacheWrapper(
      cacheKey,
      async () => {
        // Fetch market data
        const marketData = await mongoose.connection.db.collection('marketData').find({
          symbol: symbol.toUpperCase(),
          timeframe
        })
        .sort({ timestamp: -1 })
        .limit(config.APP_SETTINGS.maxDataPoints)
        .toArray();

        if (!marketData || marketData.length === 0) {
          return null; // Will be handled below
        }

        // Generate signals
        return generateSignals(marketData);
      },
      cacheTTL
    );

    if (!result) {
      return res.status(404).json({
        status: 'error',
        message: 'No market data found for the specified symbol and timeframe'
      });
    }

    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error(`Error generating signals: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate trading signals'
    });
  }
});

// Job queue management endpoints
apiRouter.post('/jobs/market-data', authenticateToken, async (req, res) => {
  try {
    const { symbol, timeframe } = req.body;

    if (!symbol || !timeframe) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol and timeframe are required'
      });
    }

    // Add job to queue
    const job = await marketDataQueue.add('fetch-market-data', {
      symbol: symbol.toUpperCase(),
      timeframe
    }, {
      delay: 0,
      attempts: 3,
      backoff: 'exponential'
    });

    res.json({
      status: 'success',
      data: {
        jobId: job.id,
        symbol,
        timeframe,
        message: 'Market data fetch job queued'
      }
    });

  } catch (error) {
    logger.error('Error queuing market data job:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to queue market data job'
    });
  }
});

apiRouter.post('/jobs/signals', authenticateToken, async (req, res) => {
  try {
    const { symbol, timeframe } = req.body;

    if (!symbol || !timeframe) {
      return res.status(400).json({
        status: 'error',
        message: 'Symbol and timeframe are required'
      });
    }

    // Add job to queue
    const job = await signalQueue.add('generate-signals', {
      symbol: symbol.toUpperCase(),
      timeframe
    }, {
      delay: 0,
      attempts: 3,
      backoff: 'exponential'
    });

    res.json({
      status: 'success',
      data: {
        jobId: job.id,
        symbol,
        timeframe,
        message: 'Signal generation job queued'
      }
    });

  } catch (error) {
    logger.error('Error queuing signal generation job:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to queue signal generation job'
    });
  }
});

apiRouter.get('/jobs/status/:jobId', authenticateToken, async (req, res) => {
  try {
    const { jobId } = req.params;

    // Check both queues for the job
    let job = await marketDataQueue.getJob(jobId);
    if (!job) {
      job = await signalQueue.getJob(jobId);
    }

    if (!job) {
      return res.status(404).json({
        status: 'error',
        message: 'Job not found'
      });
    }

    res.json({
      status: 'success',
      data: {
        id: job.id,
        name: job.name,
        data: job.data,
        progress: job.progress(),
        state: await job.getState(),
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : null,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : null,
        failedReason: job.failedReason
      }
    });

  } catch (error) {
    logger.error('Error getting job status:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get job status'
    });
  }
});

// Mount API router
app.use('/api', apiRouter);

// Serve static files for the frontend
app.use(express.static(path.join(__dirname, 'public')));

// Handle SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info(`Client connected: ${socket.id}`);

  // Join room for real-time market data
  socket.on('join-market', (symbol) => {
    socket.join(`market-${symbol}`);
    logger.info(`Client ${socket.id} joined market room: ${symbol}`);
  });

  // Leave market room
  socket.on('leave-market', (symbol) => {
    socket.leave(`market-${symbol}`);
    logger.info(`Client ${socket.id} left market room: ${symbol}`);
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    logger.info(`Client disconnected: ${socket.id}`);
  });
});

// Function to broadcast market data updates
const broadcastMarketUpdate = (symbol, data) => {
  io.to(`market-${symbol}`).emit('market-update', {
    symbol,
    data,
    timestamp: new Date().toISOString()
  });
};

// Function to broadcast trading signals
const broadcastSignal = (symbol, signal) => {
  io.to(`market-${symbol}`).emit('trading-signal', {
    symbol,
    signal,
    timestamp: new Date().toISOString()
  });
};

// Make broadcast functions available globally
global.broadcastMarketUpdate = broadcastMarketUpdate;
global.broadcastSignal = broadcastSignal;

// Start the server
server.listen(PORT, () => {
  logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
  logger.info(`Health check available at http://localhost:${PORT}/health`);
  logger.info(`Socket.IO enabled for real-time updates`);
});

// Handle graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Received shutdown signal, shutting down gracefully');

  server.close(() => {
    logger.info('HTTP server closed');
  });

  try {
    await mongoose.connection.close();
    logger.info('MongoDB connection closed');

    await redis.quit();
    logger.info('Redis connection closed');

    process.exit(0);
  } catch (err) {
    logger.error('Error during shutdown:', err);
    process.exit(1);
  }
};

// Handle termination signals
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught Exception: ${err.message}\nStack: ${err.stack}`);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Promise Rejection:');
  logger.error(reason);
});

module.exports = app; // Export for testing