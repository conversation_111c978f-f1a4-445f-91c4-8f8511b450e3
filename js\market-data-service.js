/**
 * Market Data Service for Trading Signals App
 * 
 * This service provides functions to fetch and process market data
 * from various sources using the unified API service.
 */

// Market data configuration
const MARKET_DATA_CONFIG = {
  // Default settings
  DEFAULT_SYMBOL: 'BTCUSD',
  DEFAULT_INTERVAL: '1h',
  DEFAULT_ASSET_TYPE: 'crypto',
  
  // Supported asset types
  ASSET_TYPES: ['crypto', 'forex', 'stock', 'index', 'commodity'],
  
  // Supported intervals
  INTERVALS: {
    '1m': { name: '1 Minute', alphavantage: '1min', fred: 'minute' },
    '5m': { name: '5 Minutes', alphavantage: '5min', fred: 'minute' },
    '15m': { name: '15 Minutes', alphavantage: '15min', fred: 'minute' },
    '30m': { name: '30 Minutes', alphavantage: '30min', fred: 'minute' },
    '1h': { name: '1 Hour', alphavantage: '60min', fred: 'hour' },
    '4h': { name: '4 Hours', alphavantage: 'daily', fred: 'hour' },
    '1d': { name: '1 Day', alphavantage: 'daily', fred: 'daily' },
    '1w': { name: '1 Week', alphavantage: 'weekly', fred: 'weekly' },
    '1M': { name: '1 Month', alphavantage: 'monthly', fred: 'monthly' }
  },
  
  // Popular symbols by asset type
  POPULAR_SYMBOLS: {
    crypto: ['BTCUSD', 'ETHUSD', 'XRPUSD', 'LTCUSD', 'BCHUSD', 'ADAUSD', 'DOTUSD', 'DOGEUSD'],
    forex: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD', 'EURJPY'],
    stock: ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'META', 'TSLA', 'NVDA', 'JPM'],
    index: ['SPX', 'DJI', 'IXIC', 'RUT', 'VIX', 'FTSE', 'DAX', 'N225'],
    commodity: ['GC', 'SI', 'CL', 'NG', 'HG', 'ZC', 'ZS', 'KC']
  }
};

/**
 * Market Data Service Class
 */
class MarketDataService {
  constructor() {
    // Current state
    this.currentSymbol = MARKET_DATA_CONFIG.DEFAULT_SYMBOL;
    this.currentInterval = MARKET_DATA_CONFIG.DEFAULT_INTERVAL;
    this.currentAssetType = MARKET_DATA_CONFIG.DEFAULT_ASSET_TYPE;
    
    // Data cache
    this.dataCache = new Map();
    
    // Event listeners
    this.eventListeners = {
      dataUpdated: [],
      error: []
    };
    
    console.log('Market Data Service initialized');
  }
  
  /**
   * Get market data for a symbol
   * 
   * @param {string} symbol - Market symbol
   * @param {string} interval - Time interval
   * @param {string} assetType - Asset type
   * @returns {Promise<Object>} - Market data
   */
  async getMarketData(symbol = this.currentSymbol, interval = this.currentInterval, assetType = this.currentAssetType) {
    try {
      // Update current state
      this.currentSymbol = symbol;
      this.currentInterval = interval;
      this.currentAssetType = assetType;
      
      // Generate cache key
      const cacheKey = `${symbol}_${interval}_${assetType}`;
      
      // Check if we have cached data
      if (this.dataCache.has(cacheKey)) {
        const cachedData = this.dataCache.get(cacheKey);
        
        // Check if data is still fresh (less than 1 minute old for short intervals)
        const now = Date.now();
        const maxAge = interval === '1m' || interval === '5m' ? 60 * 1000 : 5 * 60 * 1000;
        
        if (now - cachedData.timestamp < maxAge) {
          console.log(`Using cached data for ${symbol} (${interval})`);
          return cachedData.data;
        }
      }
      
      // Determine which API to use based on asset type
      let provider, endpoint, params;
      
      switch (assetType) {
        case 'crypto':
          provider = 'ALPHA_VANTAGE';
          endpoint = 'CRYPTO_INTRADAY';
          params = {
            symbol,
            market: 'USD',
            interval: MARKET_DATA_CONFIG.INTERVALS[interval].alphavantage,
            outputsize: 'compact'
          };
          break;
          
        case 'forex':
          provider = 'ALPHA_VANTAGE';
          endpoint = 'FX_INTRADAY';
          params = {
            from_symbol: symbol.substring(0, 3),
            to_symbol: symbol.substring(3, 6),
            interval: MARKET_DATA_CONFIG.INTERVALS[interval].alphavantage,
            outputsize: 'compact'
          };
          break;
          
        case 'stock':
        case 'index':
        case 'commodity':
        default:
          provider = 'ALPHA_VANTAGE';
          
          if (interval === '1d' || interval === '1w' || interval === '1M') {
            endpoint = 'TIME_SERIES_DAILY';
            params = {
              symbol,
              outputsize: 'compact'
            };
          } else {
            endpoint = 'TIME_SERIES_INTRADAY';
            params = {
              symbol,
              interval: MARKET_DATA_CONFIG.INTERVALS[interval].alphavantage,
              outputsize: 'compact'
            };
          }
          break;
      }
      
      // Fetch data using unified API service
      const response = await window.unifiedAPIService.fetchData({
        provider,
        endpoint,
        params,
        useCache: true,
        cacheTTL: 60 * 1000 // 1 minute
      });
      
      // Process the data
      const processedData = this.processMarketData(response.data, interval, assetType);
      
      // Cache the data
      this.dataCache.set(cacheKey, {
        data: processedData,
        timestamp: Date.now()
      });
      
      // Trigger data updated event
      this.triggerEvent('dataUpdated', processedData);
      
      return processedData;
    } catch (error) {
      console.error('Error fetching market data:', error);
      
      // Trigger error event
      this.triggerEvent('error', {
        message: `Failed to fetch market data for ${symbol}: ${error.message}`,
        error
      });
      
      // Return mock data as fallback
      return this.getMockMarketData(symbol, interval, assetType);
    }
  }
  
  /**
   * Process market data from API response
   * 
   * @param {Object} data - API response data
   * @param {string} interval - Time interval
   * @param {string} assetType - Asset type
   * @returns {Object} - Processed market data
   */
  processMarketData(data, interval, assetType) {
    try {
      // Initialize result object
      const result = {
        symbol: this.currentSymbol,
        interval,
        assetType,
        dates: [],
        opens: [],
        highs: [],
        lows: [],
        closes: [],
        volumes: [],
        lastUpdate: new Date().toISOString()
      };
      
      // Handle different data formats based on the endpoint
      if (data['Time Series (Digital Currency Intraday)']) {
        // Crypto intraday data
        const timeSeries = data['Time Series (Digital Currency Intraday)'];
        
        Object.keys(timeSeries).forEach(date => {
          const entry = timeSeries[date];
          
          result.dates.push(date);
          result.opens.push(parseFloat(entry['1. open']));
          result.highs.push(parseFloat(entry['2. high']));
          result.lows.push(parseFloat(entry['3. low']));
          result.closes.push(parseFloat(entry['4. close']));
          result.volumes.push(parseFloat(entry['5. volume']));
        });
      } else if (data['Time Series FX (Intraday)']) {
        // Forex intraday data
        const timeSeries = data['Time Series FX (Intraday)'];
        
        Object.keys(timeSeries).forEach(date => {
          const entry = timeSeries[date];
          
          result.dates.push(date);
          result.opens.push(parseFloat(entry['1. open']));
          result.highs.push(parseFloat(entry['2. high']));
          result.lows.push(parseFloat(entry['3. low']));
          result.closes.push(parseFloat(entry['4. close']));
          result.volumes.push(0); // Forex doesn't have volume
        });
      } else if (data['Time Series (Intraday)']) {
        // Stock intraday data
        const timeSeries = data['Time Series (Intraday)'];
        
        Object.keys(timeSeries).forEach(date => {
          const entry = timeSeries[date];
          
          result.dates.push(date);
          result.opens.push(parseFloat(entry['1. open']));
          result.highs.push(parseFloat(entry['2. high']));
          result.lows.push(parseFloat(entry['3. low']));
          result.closes.push(parseFloat(entry['4. close']));
          result.volumes.push(parseFloat(entry['5. volume']));
        });
      } else if (data['Time Series (Daily)']) {
        // Daily data
        const timeSeries = data['Time Series (Daily)'];
        
        Object.keys(timeSeries).forEach(date => {
          const entry = timeSeries[date];
          
          result.dates.push(date);
          result.opens.push(parseFloat(entry['1. open']));
          result.highs.push(parseFloat(entry['2. high']));
          result.lows.push(parseFloat(entry['3. low']));
          result.closes.push(parseFloat(entry['4. close']));
          result.volumes.push(parseFloat(entry['5. volume']));
        });
      } else if (data['Global Quote']) {
        // Global quote data (current price only)
        const quote = data['Global Quote'];
        
        const date = quote['07. latest trading day'] || new Date().toISOString().split('T')[0];
        
        result.dates.push(date);
        result.opens.push(parseFloat(quote['02. open']));
        result.highs.push(parseFloat(quote['03. high']));
        result.lows.push(parseFloat(quote['04. low']));
        result.closes.push(parseFloat(quote['05. price']));
        result.volumes.push(parseFloat(quote['06. volume']));
      } else {
        console.warn('Unknown data format:', data);
        throw new Error('Unknown data format');
      }
      
      // Sort data by date (oldest first)
      const sortedIndices = result.dates
        .map((date, index) => ({ date, index }))
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .map(item => item.index);
      
      result.dates = sortedIndices.map(i => result.dates[i]);
      result.opens = sortedIndices.map(i => result.opens[i]);
      result.highs = sortedIndices.map(i => result.highs[i]);
      result.lows = sortedIndices.map(i => result.lows[i]);
      result.closes = sortedIndices.map(i => result.closes[i]);
      result.volumes = sortedIndices.map(i => result.volumes[i]);
      
      return result;
    } catch (error) {
      console.error('Error processing market data:', error);
      throw error;
    }
  }
  
  /**
   * Get mock market data as fallback
   * 
   * @param {string} symbol - Market symbol
   * @param {string} interval - Time interval
   * @param {string} assetType - Asset type
   * @returns {Object} - Mock market data
   */
  getMockMarketData(symbol = this.currentSymbol, interval = this.currentInterval, assetType = this.currentAssetType) {
    console.log(`Generating mock data for ${symbol} (${interval})`);
    
    // Generate dates based on interval
    const dates = [];
    const opens = [];
    const highs = [];
    const lows = [];
    const closes = [];
    const volumes = [];
    
    const now = new Date();
    let basePrice = 100;
    
    // Set base price based on symbol
    if (symbol.includes('BTC')) basePrice = 30000;
    else if (symbol.includes('ETH')) basePrice = 2000;
    else if (symbol.includes('EUR')) basePrice = 1.1;
    else if (symbol.includes('GBP')) basePrice = 1.3;
    else if (symbol.includes('JPY')) basePrice = 110;
    else if (symbol === 'AAPL') basePrice = 180;
    else if (symbol === 'MSFT') basePrice = 350;
    else if (symbol === 'GOOGL') basePrice = 140;
    
    // Generate data points
    const numPoints = 100;
    let price = basePrice;
    
    for (let i = 0; i < numPoints; i++) {
      // Calculate date based on interval
      let date = new Date(now);
      
      switch (interval) {
        case '1m':
          date.setMinutes(date.getMinutes() - (numPoints - i));
          break;
        case '5m':
          date.setMinutes(date.getMinutes() - (numPoints - i) * 5);
          break;
        case '15m':
          date.setMinutes(date.getMinutes() - (numPoints - i) * 15);
          break;
        case '30m':
          date.setMinutes(date.getMinutes() - (numPoints - i) * 30);
          break;
        case '1h':
          date.setHours(date.getHours() - (numPoints - i));
          break;
        case '4h':
          date.setHours(date.getHours() - (numPoints - i) * 4);
          break;
        case '1d':
          date.setDate(date.getDate() - (numPoints - i));
          break;
        case '1w':
          date.setDate(date.getDate() - (numPoints - i) * 7);
          break;
        case '1M':
          date.setMonth(date.getMonth() - (numPoints - i));
          break;
      }
      
      // Generate random price movement
      const change = (Math.random() - 0.5) * 0.02 * price;
      price += change;
      
      const open = price;
      const close = price + (Math.random() - 0.5) * 0.01 * price;
      const high = Math.max(open, close) + Math.random() * 0.01 * price;
      const low = Math.min(open, close) - Math.random() * 0.01 * price;
      const volume = Math.floor(Math.random() * 1000000);
      
      dates.push(date.toISOString());
      opens.push(parseFloat(open.toFixed(2)));
      highs.push(parseFloat(high.toFixed(2)));
      lows.push(parseFloat(low.toFixed(2)));
      closes.push(parseFloat(close.toFixed(2)));
      volumes.push(volume);
    }
    
    return {
      symbol,
      interval,
      assetType,
      dates,
      opens,
      highs,
      lows,
      closes,
      volumes,
      lastUpdate: new Date().toISOString(),
      isMock: true
    };
  }
  
  /**
   * Add event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  addEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      this.eventListeners[event] = [];
    }
    
    this.eventListeners[event].push(callback);
  }
  
  /**
   * Remove event listener
   * 
   * @param {string} event - Event name
   * @param {Function} callback - Callback function
   */
  removeEventListener(event, callback) {
    if (!this.eventListeners[event]) {
      return;
    }
    
    this.eventListeners[event] = this.eventListeners[event].filter(cb => cb !== callback);
  }
  
  /**
   * Trigger event
   * 
   * @param {string} event - Event name
   * @param {*} data - Event data
   */
  triggerEvent(event, data) {
    if (!this.eventListeners[event]) {
      return;
    }
    
    this.eventListeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} event listener:`, error);
      }
    });
  }
}

// Create singleton instance
const marketDataService = new MarketDataService();

// Export for use in other modules
if (typeof window !== 'undefined') {
  window.marketDataService = marketDataService;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = marketDataService;
}
