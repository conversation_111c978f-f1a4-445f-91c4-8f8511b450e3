/**
 * Trading Signals Module for Trading Signals App
 * 
 * This file handles creating, retrieving, updating, and deleting trading signals.
 */

// API base URL
const API_BASE_URL = '/api/mongodb';
let socket;

// Current signals data
let signalsData = [];

// Initialize the trading signals module
function initTradingSignals() {
  // Check if user is authenticated
  if (!window.Auth || !window.Auth.isAuthenticated()) {
    // Redirect to login page
    window.location.href = '/auth.html';
    return;
  }
  
  // Load signals data
  loadSignals();
  
  // Set up event listeners
  setupEventListeners();
  
  // Initialize WebSocket connection
  initWebSocket();
}

// Initialize WebSocket connection for real-time updates
function initWebSocket() {
  // Check if Socket.io is loaded
  if (typeof io === 'undefined') {
    console.error('Socket.io is not loaded. Real-time updates will not be available.');
    return;
  }
  
  // Get the user ID from authentication
  const userId = window.Auth.getUserId();
  if (!userId) {
    console.error('User ID not available. Real-time updates will not be available.');
    return;
  }
  
  // Connect to WebSocket server
  socket = io(window.location.origin);
  
  // Handle connection events
  socket.on('connect', () => {
    console.log('WebSocket connected successfully');
    
    // Join user-specific room
    socket.emit('join', { userId });
    
    // Join symbol-specific rooms based on current signals
    const symbols = [...new Set(signalsData.map(signal => signal.symbol))];
    if (symbols.length > 0) {
      socket.emit('join', { symbols });
    }
  });
  
  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('WebSocket disconnected');
  });
  
  // Handle reconnection
  socket.on('reconnect', () => {
    console.log('WebSocket reconnected');
  });
  
  // Handle signal updates
  socket.on('signal_update', (data) => {
    console.log('Received signal update:', data);
    
    // Find the signal in the current data
    const index = signalsData.findIndex(signal => signal._id === data._id);
    
    if (index !== -1) {
      // Update existing signal
      signalsData[index] = data;
    } else {
      // Add new signal
      signalsData.push(data);
    }
    
    // Update UI
    displaySignals(signalsData);
  });
  
  // Handle signal deletion
  socket.on('signal_delete', (data) => {
    console.log('Received signal deletion:', data);
    
    // Remove the signal from the current data
    signalsData = signalsData.filter(signal => signal._id !== data._id);
    
    // Update UI
    displaySignals(signalsData);
  });
}

// Set up event listeners
function setupEventListeners() {
  // Add signal form submission
  const signalForm = document.getElementById('signal-form');
  if (signalForm) {
    signalForm.addEventListener('submit', handleSignalSubmit);
  }
  
  // Filter form submission
  const filterForm = document.getElementById('filter-form');
  if (filterForm) {
    filterForm.addEventListener('submit', handleFilterSubmit);
  }
}

// Load trading signals from the server
async function loadSignals(filters = {}) {
  try {
    // Build query string from filters
    const queryParams = new URLSearchParams();
    if (filters.symbol) queryParams.append('symbol', filters.symbol);
    if (filters.type) queryParams.append('type', filters.type);
    if (filters.timeframe) queryParams.append('timeframe', filters.timeframe);
    
    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : '';
    
    // Fetch signals from the server
    const response = await fetch(`${API_BASE_URL}/signals${queryString}`, {
      method: 'GET',
      headers: window.Auth.getAuthHeaders()
    });
    
    if (!response.ok) {
      throw new Error('Failed to load signals');
    }
    
    // Parse response
    const data = await response.json();
    
    // Store signals data
    signalsData = data;
    
    // Display signals
    displaySignals(signalsData);
    
    return data;
  } catch (error) {
    console.error('Error loading signals:', error);
    showMessage('error', 'Failed to load signals: ' + error.message);
  }
}

// Display signals in the UI
function displaySignals(signals) {
  const signalsContainer = document.getElementById('signals-container');
  if (!signalsContainer) return;
  
  if (signals.length === 0) {
    signalsContainer.innerHTML = '<p>No trading signals found. Create your first signal!</p>';
    return;
  }
  
  // Create HTML for signals
  const signalsHTML = signals.map(signal => `
    <div class="signal-card ${signal.type.toLowerCase()}" data-id="${signal._id}">
      <div class="signal-header">
        <h3>${signal.symbol} - ${signal.type}</h3>
        <span class="signal-timeframe">${signal.timeframe}</span>
      </div>
      <div class="signal-body">
        <div class="signal-prices">
          <p><strong>Entry:</strong> ${signal.entryPrice}</p>
          <p><strong>Stop Loss:</strong> ${signal.stopLoss}</p>
          <p><strong>Take Profit:</strong> ${signal.takeProfit}</p>
        </div>
        <div class="signal-meta">
          <p><strong>Status:</strong> ${signal.status}</p>
          <p><strong>Created:</strong> ${new Date(signal.createdAt).toLocaleString()}</p>
        </div>
        ${signal.analysis ? `<div class="signal-analysis"><p>${signal.analysis}</p></div>` : ''}
      </div>
      <div class="signal-actions">
        <button class="btn btn-sm btn-edit" data-id="${signal._id}">Edit</button>
        <button class="btn btn-sm btn-delete" data-id="${signal._id}">Delete</button>
      </div>
    </div>
  `).join('');
  
  signalsContainer.innerHTML = signalsHTML;
  
  // Add event listeners to buttons
  document.querySelectorAll('.btn-edit').forEach(button => {
    button.addEventListener('click', () => editSignal(button.dataset.id));
  });
  
  document.querySelectorAll('.btn-delete').forEach(button => {
    button.addEventListener('click', () => deleteSignal(button.dataset.id));
  });
}

// Create a new trading signal
async function createSignal(signalData) {
  try {
    const response = await fetch(`${API_BASE_URL}/signals`, {
      method: 'POST',
      headers: window.Auth.getAuthHeaders(),
      body: JSON.stringify(signalData)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create signal');
    }
    
    const data = await response.json();
    
    // Show success message
    showMessage('success', 'Trading signal created successfully!');
    
    // Reload signals
    loadSignals();
    
    // Reset form
    resetSignalForm();
    
    return data;
  } catch (error) {
    console.error('Error creating signal:', error);
    showMessage('error', 'Failed to create signal: ' + error.message);
    throw error;
  }
}

// Update an existing trading signal
async function updateSignal(signalId, signalData) {
  try {
    const response = await fetch(`${API_BASE_URL}/signals/${signalId}`, {
      method: 'PUT',
      headers: window.Auth.getAuthHeaders(),
      body: JSON.stringify(signalData)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to update signal');
    }
    
    const data = await response.json();
    
    // Show success message
    showMessage('success', 'Trading signal updated successfully!');
    
    // Reload signals
    loadSignals();
    
    // Reset form
    resetSignalForm();
    
    return data;
  } catch (error) {
    console.error('Error updating signal:', error);
    showMessage('error', 'Failed to update signal: ' + error.message);
    throw error;
  }
}

// Delete a trading signal
async function deleteSignal(signalId) {
  // Confirm deletion
  if (!confirm('Are you sure you want to delete this signal?')) {
    return;
  }
  
  try {
    const response = await fetch(`${API_BASE_URL}/signals/${signalId}`, {
      method: 'DELETE',
      headers: window.Auth.getAuthHeaders()
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to delete signal');
    }
    
    // Show success message
    showMessage('success', 'Trading signal deleted successfully!');
    
    // Reload signals
    loadSignals();
    
    return true;
  } catch (error) {
    console.error('Error deleting signal:', error);
    showMessage('error', 'Failed to delete signal: ' + error.message);
    throw error;
  }
}

// Edit a signal (populate form with signal data)
function editSignal(signalId) {
  // Find the signal in the data
  const signal = signalsData.find(s => s._id === signalId);
  if (!signal) return;
  
  // Get the form
  const form = document.getElementById('signal-form');
  if (!form) return;
  
  // Set form to edit mode
  form.dataset.mode = 'edit';
  form.dataset.signalId = signalId;
  
  // Populate form fields
  document.getElementById('signal-symbol').value = signal.symbol;
  document.getElementById('signal-type').value = signal.type;
  document.getElementById('signal-entry-price').value = signal.entryPrice;
  document.getElementById('signal-stop-loss').value = signal.stopLoss;
  document.getElementById('signal-take-profit').value = signal.takeProfit;
  document.getElementById('signal-timeframe').value = signal.timeframe;
  
  if (document.getElementById('signal-analysis')) {
    document.getElementById('signal-analysis').value = signal.analysis || '';
  }
  
  // Update submit button text
  const submitButton = form.querySelector('button[type="submit"]');
  if (submitButton) {
    submitButton.textContent = 'Update Signal';
  }
  
  // Scroll to form
  form.scrollIntoView({ behavior: 'smooth' });
}

// Reset the signal form
function resetSignalForm() {
  const form = document.getElementById('signal-form');
  if (!form) return;
  
  // Reset form mode
  form.dataset.mode = 'create';
  delete form.dataset.signalId;
  
  // Clear form fields
  form.reset();
  
  // Reset submit button text
  const submitButton = form.querySelector('button[type="submit"]');
  if (submitButton) {
    submitButton.textContent = 'Create Signal';
  }
}

// Handle signal form submission
async function handleSignalSubmit(e) {
  e.preventDefault();
  
  // Get form data
  const form = e.target;
  const formData = new FormData(form);
  
  // Create signal data object
  const signalData = {
    symbol: formData.get('symbol').toUpperCase(),
    type: formData.get('type'),
    entryPrice: parseFloat(formData.get('entryPrice')),
    stopLoss: parseFloat(formData.get('stopLoss')),
    takeProfit: parseFloat(formData.get('takeProfit')),
    timeframe: formData.get('timeframe')
  };
  
  // Add optional fields if they exist
  if (formData.has('analysis')) {
    signalData.analysis = formData.get('analysis');
  }
  
  try {
    if (form.dataset.mode === 'edit') {
      // Update existing signal
      await updateSignal(form.dataset.signalId, signalData);
    } else {
      // Create new signal
      await createSignal(signalData);
    }
  } catch (error) {
    console.error('Error submitting signal:', error);
  }
}

// Handle filter form submission
function handleFilterSubmit(e) {
  e.preventDefault();
  
  // Get form data
  const form = e.target;
  const formData = new FormData(form);
  
  // Create filters object
  const filters = {};
  
  // Add filters if they have values
  if (formData.get('symbol')) {
    filters.symbol = formData.get('symbol').toUpperCase();
  }
  
  if (formData.get('type')) {
    filters.type = formData.get('type');
  }
  
  if (formData.get('timeframe')) {
    filters.timeframe = formData.get('timeframe');
  }
  
  // Load signals with filters
  loadSignals(filters);
}

// Show message in the UI
function showMessage(type, message) {
  const messageElement = document.getElementById('message-container');
  if (!messageElement) return;
  
  messageElement.className = `message ${type}`;
  messageElement.textContent = message;
  
  // Clear message after 5 seconds
  setTimeout(() => {
    messageElement.textContent = '';
    messageElement.className = 'message';
  }, 5000);
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initTradingSignals();
});

// Export functions for use in other modules
window.TradingSignals = {
  loadSignals,
  createSignal,
  updateSignal,
  deleteSignal,
  resetSignalForm
};
