/**
 * Trading Signals App - Main Application Script
 *
 * This script handles the main functionality of the Trading Signals App,
 * including fetching and displaying real-time market data, updating charts,
 * and generating trading signals.
 */

// Application state
const appState = {
    // Market data
    market: {
        currentSymbol: 'EURUSD',
        currentTimeframe: 'M5',
        currentAssetType: 'forex',
        data: null,
        isLoading: false,
        hasError: false,
        errorMessage: '',
        lastUpdated: null,
        updateInterval: null
    },

    // UI state
    ui: {
        isDarkMode: false,
        isMenuOpen: false,
        activeTab: 'market'
    },

    // Chart state
    chart: {
        instance: null,
        type: 'candlestick',
        indicators: ['sma', 'volume']
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', initApp);

/**
 * Initialize the application
 */
function initApp() {
    console.log('Initializing Trading Signals App...');

    // Start progress indicator
    if (typeof NProgress !== 'undefined') {
        NProgress.start();
    }

    // Set up event listeners
    setupEventListeners();

    // Set up settings event listeners
    setupSettingsEventListeners();

    // Initialize UI components
    initializeUI();

    // Load initial market data
    loadMarketData();

    // Set up auto-refresh
    setupAutoRefresh();

    console.log('App initialization complete');

    // Complete progress indicator
    if (typeof NProgress !== 'undefined') {
        setTimeout(() => {
            NProgress.done();
        }, 500);
    }
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
    // Market selection controls
    const symbolSelect = document.getElementById('symbol');
    const timeframeSelect = document.getElementById('timeframe');
    const marketTypeSelect = document.getElementById('marketType');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const refreshBtn = document.getElementById('refreshMarketBtn');

    if (symbolSelect) {
        symbolSelect.addEventListener('change', handleSymbolChange);
    }

    if (timeframeSelect) {
        timeframeSelect.addEventListener('change', handleTimeframeChange);
    }

    if (marketTypeSelect) {
        marketTypeSelect.addEventListener('change', handleMarketTypeChange);
    }

    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', analyzeMarket);
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', loadMarketData);
    }

    // Theme toggle
    const themeToggle = document.getElementById('darkModeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', toggleTheme);
    }

    console.log('Event listeners set up');
}

/**
 * Set up settings event listeners
 */
function setupSettingsEventListeners() {
    // Settings button in navbar
    const settingsBtn = document.getElementById('settingsBtn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', openSettings);
    }

    // Settings link in user dropdown
    const settingsLink = document.getElementById('settingsLink');
    if (settingsLink) {
        settingsLink.addEventListener('click', function(event) {
            event.preventDefault();
            openSettings();
        });
    }

    console.log('Settings event listeners set up');
}

/**
 * Open settings modal
 * @param {string} tab - Tab to open (optional)
 */
function openSettings(tab) {
    // If settings manager is available, use it
    if (window.settingsManager && typeof window.settingsManager.openSettings === 'function') {
        window.settingsManager.openSettings(tab);
    } else {
        console.warn('Settings manager not available');
        showNotification('Settings manager not available. Please refresh the page and try again.', 'warning');
    }
}

/**
 * Initialize UI components
 */
function initializeUI() {
    // Set initial values for selects
    const symbolSelect = document.getElementById('symbol');
    const timeframeSelect = document.getElementById('timeframe');
    const marketTypeSelect = document.getElementById('marketType');

    if (symbolSelect) {
        symbolSelect.value = appState.market.currentSymbol;
    }

    if (timeframeSelect) {
        timeframeSelect.value = appState.market.currentTimeframe;
    }

    if (marketTypeSelect) {
        marketTypeSelect.value = appState.market.currentAssetType;
    }

    // Initialize theme
    initializeTheme();

    console.log('UI initialized');
}

/**
 * Initialize theme
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('theme_preference');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
        appState.ui.isDarkMode = savedTheme === 'dark';

        const themeToggle = document.getElementById('darkModeToggle');
        if (themeToggle) {
            themeToggle.checked = appState.ui.isDarkMode;
        }
    }
}

/**
 * Toggle theme between light and dark
 */
function toggleTheme(event) {
    const isDarkMode = event.target.checked;
    appState.ui.isDarkMode = isDarkMode;

    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
    localStorage.setItem('theme_preference', isDarkMode ? 'dark' : 'light');
}

/**
 * Handle symbol change
 */
function handleSymbolChange(event) {
    appState.market.currentSymbol = event.target.value;
    console.log(`Symbol changed to: ${appState.market.currentSymbol}`);
    loadMarketData();
}

/**
 * Handle timeframe change
 */
function handleTimeframeChange(event) {
    appState.market.currentTimeframe = event.target.value;
    console.log(`Timeframe changed to: ${appState.market.currentTimeframe}`);
    loadMarketData();
}

/**
 * Handle market type change
 */
function handleMarketTypeChange(event) {
    appState.market.currentAssetType = event.target.value;
    console.log(`Market type changed to: ${appState.market.currentAssetType}`);

    // Update symbol options based on market type
    updateSymbolOptions(appState.market.currentAssetType);

    // Load market data for new market type
    loadMarketData();
}

/**
 * Update symbol options based on market type
 */
function updateSymbolOptions(marketType) {
    const symbolSelect = document.getElementById('symbol');
    if (!symbolSelect) return;

    // Clear current options
    symbolSelect.innerHTML = '';

    // Add new options based on market type
    let options = [];

    switch (marketType) {
        case 'forex':
            options = [
                { value: 'EURUSD', text: 'EUR/USD' },
                { value: 'GBPUSD', text: 'GBP/USD' },
                { value: 'USDJPY', text: 'USD/JPY' },
                { value: 'AUDUSD', text: 'AUD/USD' },
                { value: 'USDCAD', text: 'USD/CAD' }
            ];
            break;
        case 'commodities':
            options = [
                { value: 'XAUUSD', text: 'Gold (XAU/USD)' },
                { value: 'XAGUSD', text: 'Silver (XAG/USD)' },
                { value: 'USOIL', text: 'WTI Crude Oil' },
                { value: 'UKOIL', text: 'Brent Crude Oil' }
            ];
            break;
        case 'indices':
            options = [
                { value: 'US30', text: 'Dow Jones (US30)' },
                { value: 'SPX500', text: 'S&P 500' },
                { value: 'NASDAQ', text: 'NASDAQ' },
                { value: 'GER30', text: 'DAX (GER30)' }
            ];
            break;
        case 'crypto':
            options = [
                { value: 'BTCUSD', text: 'Bitcoin (BTC/USD)' },
                { value: 'ETHUSD', text: 'Ethereum (ETH/USD)' },
                { value: 'LTCUSD', text: 'Litecoin (LTC/USD)' }
            ];
            break;
    }

    // Add options to select
    options.forEach(option => {
        const optionElement = document.createElement('option');
        optionElement.value = option.value;
        optionElement.textContent = option.text;
        symbolSelect.appendChild(optionElement);
    });

    // Set first option as selected
    if (options.length > 0) {
        appState.market.currentSymbol = options[0].value;
        symbolSelect.value = appState.market.currentSymbol;
    }
}

/**
 * Set up auto-refresh for market data - simplified version
 */
function setupAutoRefresh() {
    // Set up interval (refresh every 2 minutes)
    appState.market.updateInterval = setInterval(() => {
        loadMarketData(true);
    }, 120000); // 2 minutes
}

/**
 * Load market data - enhanced version with caching and error handling
 * @param {boolean} silent - Whether to show loading indicators
 */
async function loadMarketData(silent = false) {
    try {
        // Update loading state
        if (!silent) {
            updateLoadingState(true);
        }

        // Generate cache key
        const cacheKey = `market_data_${appState.market.currentSymbol}_${appState.market.currentTimeframe}_${appState.market.currentAssetType}`;

        // Check cache first if available
        if (window.enhancedCache) {
            const cachedData = window.enhancedCache.get(cacheKey, 'marketData');
            if (cachedData) {
                console.log('Using cached market data');

                // Update app state with cached data
                appState.market.data = cachedData;
                appState.market.lastUpdated = new Date();

                // Update UI
                updateMarketUI();
                updateChart();
                generateTradingSignals();

                return cachedData;
            }
        }

        let data;

        // Try to use data source registry if available
        if (window.dataSourceRegistry && typeof window.dataSourceRegistry.fetchData === 'function') {
            try {
                console.log('Using data source registry to fetch market data');

                // Fetch data using the registry
                data = await window.dataSourceRegistry.fetchData(
                    appState.market.currentSymbol,
                    appState.market.currentTimeframe,
                    appState.market.currentAssetType
                );
            } catch (registryError) {
                console.warn('Data source registry failed:', registryError);

                // Log error with error handler if available
                if (window.errorHandler) {
                    window.errorHandler.handleError(registryError, {
                        category: 'api',
                        source: 'data-source-registry',
                        context: {
                            symbol: appState.market.currentSymbol,
                            timeframe: appState.market.currentTimeframe,
                            assetType: appState.market.currentAssetType
                        }
                    });
                }

                // Fall back to market data service
                console.log('Falling back to market data service');
                data = await window.MarketDataService.getRealTimeMarketData(
                    appState.market.currentSymbol,
                    appState.market.currentTimeframe,
                    appState.market.currentAssetType
                );
            }
        } else {
            // Fall back to market data service
            console.log('Data source registry not available, using market data service');
            data = await window.MarketDataService.getRealTimeMarketData(
                appState.market.currentSymbol,
                appState.market.currentTimeframe,
                appState.market.currentAssetType
            );
        }

        // Cache the data if cache is available
        if (window.enhancedCache && data) {
            window.enhancedCache.set(cacheKey, data, 'marketData');
        }

        // Update app state with new data
        appState.market.data = data;
        appState.market.lastUpdated = new Date();

        // Update UI
        updateMarketUI();
        updateChart();
        generateTradingSignals();

        return data;
    } catch (error) {
        console.error('Error loading market data:', error);

        // Log error with error handler if available
        if (window.errorHandler) {
            window.errorHandler.handleError(error, {
                category: 'api',
                source: 'load-market-data',
                context: {
                    symbol: appState.market.currentSymbol,
                    timeframe: appState.market.currentTimeframe,
                    assetType: appState.market.currentAssetType
                },
                retry: true,
                retryFn: () => loadMarketData(silent),
                maxRetries: 3
            });
        }

        // Update error state
        updateErrorState('Data loading failed: ' + error.message);
        return null;
    }
}

/**
 * Update market UI with current data
 */
function updateMarketUI() {
    // Reset loading state
    updateLoadingState(false);

    const data = appState.market.data;
    if (!data) return;

    // Update current symbol
    const currentSymbolElement = document.getElementById('currentSymbol');
    if (currentSymbolElement) {
        currentSymbolElement.textContent = data.symbol;
    }

    // Update current price
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement) {
        currentPriceElement.textContent = data.currentPrice.toFixed(5);

        // Add data source indicator
        if (data.isMockData) {
            currentPriceElement.innerHTML += ' <span class="badge bg-warning text-dark" title="Using simulated data due to API limitations">DEMO</span>';
        } else if (data.source) {
            let badgeClass = 'bg-info';
            let badgeTitle = 'Data source';

            // Customize badge based on source
            if (data.source === 'integrated-api') {
                badgeClass = 'bg-success';
                badgeTitle = 'Using integrated multi-API data source';
            } else if (data.source === 'alpha-vantage') {
                badgeClass = 'bg-primary';
                badgeTitle = 'Using Alpha Vantage API';
            } else if (data.source === 'polygon') {
                badgeClass = 'bg-info';
                badgeTitle = 'Using Polygon.io API';
            } else if (data.source === 'fmp') {
                badgeClass = 'bg-secondary';
                badgeTitle = 'Using Financial Modeling Prep API';
            }

            currentPriceElement.innerHTML += ` <span class="badge ${badgeClass}" title="${badgeTitle}">${data.source.toUpperCase()}</span>`;
        }
    }

    // Update daily change
    const dailyChangeElement = document.getElementById('dailyChange');
    if (dailyChangeElement && data.dailyChange !== undefined) {
        const isPositive = data.dailyChange >= 0;
        dailyChangeElement.textContent = `${isPositive ? '+' : ''}${data.dailyChange.toFixed(2)}%`;
        dailyChangeElement.className = `badge ${isPositive ? 'bg-success' : 'bg-danger'}`;
    }

    // Update last update time
    const lastUpdateElement = document.getElementById('lastUpdate');
    if (lastUpdateElement) {
        lastUpdateElement.textContent = new Date().toLocaleTimeString();
    }

    // Update market sentiment
    updateSentimentUI(data.sentiment);

    // Show notification based on data source
    if (data.isMockData) {
        showNotification('Using simulated market data due to API limitations. The data shown is for demonstration purposes only.', 'warning');
    } else if (data.source === 'integrated-api') {
        showNotification('Using real market data from the integrated multi-API service.', 'success');
    }
}

/**
 * Update sentiment UI
 * @param {number} sentiment - Sentiment value (0-100)
 */
function updateSentimentUI(sentiment) {
    const progressBar = document.querySelector('.progress-bar');
    if (!progressBar) return;

    progressBar.style.width = `${sentiment}%`;
    progressBar.setAttribute('aria-valuenow', sentiment);

    // Update color based on sentiment
    if (sentiment > 60) {
        progressBar.className = 'progress-bar bg-success';
        progressBar.textContent = `${sentiment}% Bullish`;
    } else if (sentiment < 40) {
        progressBar.className = 'progress-bar bg-danger';
        progressBar.textContent = `${sentiment}% Bearish`;
    } else {
        progressBar.className = 'progress-bar bg-warning';
        progressBar.textContent = `${sentiment}% Neutral`;
    }
}

/**
 * Update loading state
 * @param {boolean} isLoading - Whether the app is loading data
 */
function updateLoadingState(isLoading) {
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement && isLoading) {
        currentPriceElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading...';
    }
}

/**
 * Update error state
 * @param {string} errorMessage - Error message to display
 */
function updateErrorState(errorMessage) {
    const currentPriceElement = document.getElementById('currentPrice');
    if (currentPriceElement) {
        currentPriceElement.innerHTML = `<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error: ${errorMessage}</span>`;
    }
}

/**
 * Show notification to the user - simplified version
 * @param {string} message - Notification message
 * @param {string} type - Notification type (success, info, warning, danger)
 */
function showNotification(message, type = 'info') {
    // Create simple alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.innerHTML = message;
    alert.style.position = 'fixed';
    alert.style.top = '20px';
    alert.style.right = '20px';
    alert.style.zIndex = '9999';
    alert.style.maxWidth = '350px';

    // Add to body
    document.body.appendChild(alert);

    // Remove after 3 seconds
    setTimeout(() => {
        alert.remove();
    }, 3000);
}

/**
 * Update chart with current data
 */
function updateChart() {
    console.log('Updating chart with new data...');

    // Check if we have market data
    if (!appState.market.data) {
        console.warn('No market data available for chart update');
        return;
    }

    try {
        // If chart.js is loaded and updateChart function is available, call it
        if (window.updateChart && typeof window.updateChart === 'function') {
            window.updateChart(appState.market.data);
        } else if (window.chartModule && typeof window.chartModule.updateChart === 'function') {
            window.chartModule.updateChart(appState.market.data);
        } else {
            console.error('Chart update function not found. Make sure chart.js is properly loaded.');
        }
    } catch (error) {
        console.error('Error updating chart:', error);
    }
}

/**
 * Analyze market and generate trading signals
 */
function analyzeMarket() {
    console.log('Analyzing market...');
    generateTradingSignals();
}

/**
 * Generate trading signals based on market data
 */
function generateTradingSignals() {
    try {
        // This function generates trading signals based on technical indicators and market data
        console.log('Generating trading signals...');

        // Check if market data is available
        if (!appState.market.data) {
            console.warn('No market data available for signal generation');
            updateSignalsContainer(null);
            return;
        }

        // If technical-analysis.js is loaded, use its generateTradingSignal function
        if (window.technicalAnalysis && typeof window.technicalAnalysis.generateTradingSignal === 'function') {
            // Create a copy of the data to avoid reference issues
            const marketData = { ...appState.market.data };

            // Generate the signal
            const signal = window.technicalAnalysis.generateTradingSignal(marketData);
            console.log('Generated trading signal:', signal);

            // Update signals container
            updateSignalsContainer(signal);
        } else {
            console.warn('Technical analysis module not loaded');

            // Show warning in signals container
            const signalsContainer = document.getElementById('signalsContainer');
            if (signalsContainer) {
                signalsContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <h4>Technical Analysis Not Available</h4>
                        <p>The technical analysis module is not loaded. Please refresh the page and try again.</p>
                    </div>
                `;
            }
        }
    } catch (error) {
        console.error('Error generating trading signals:', error);

        // Show error in signals container
        const signalsContainer = document.getElementById('signalsContainer');
        if (signalsContainer) {
            signalsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h4>Error Generating Signals</h4>
                    <p>${error.message}</p>
                </div>
            `;
        }
    }
}

/**
 * Update signals container with trading signals
 * @param {Object} signal - Trading signal data
 */
function updateSignalsContainer(signal) {
    try {
        const signalsContainer = document.getElementById('signalsContainer');
        if (!signalsContainer) return;

        // Clear container
        signalsContainer.innerHTML = '';

        // Check if signal is valid
        if (!signal) {
            console.error('Invalid signal data');
            signalsContainer.innerHTML = `
                <div class="alert alert-warning">
                    <h4>No Trading Signal Available</h4>
                    <p>Unable to generate a trading signal with the current market data.</p>
                </div>
            `;
            return;
        }

        // Create signal element
        const signalElement = document.createElement('div');
        signalElement.className = `alert alert-${getSignalAlertClass(signal.type)}`;

        // Format numbers safely
        const formatNumber = (num, decimals = 5) => {
            if (num === null || num === undefined || isNaN(num)) return 'N/A';
            return parseFloat(num).toFixed(decimals);
        };

        // Set signal content
        signalElement.innerHTML = `
            <h4>${getSignalTypeText(signal.type)}</h4>
            <p><strong>Entry Price:</strong> ${formatNumber(signal.entryPrice)}</p>
            <p><strong>Stop Loss:</strong> ${formatNumber(signal.stopLoss)}</p>
            <p><strong>Take Profit:</strong> ${formatNumber(signal.takeProfit)}</p>
            <p><strong>Risk/Reward Ratio:</strong> 1:${formatNumber(signal.riskRewardRatio, 2)}</p>
            <p><strong>Confidence:</strong> ${formatNumber(signal.confidence * 100, 0)}%</p>
            <p><strong>Generated:</strong> ${new Date().toLocaleTimeString()}</p>
        `;

        // Add signal to container
        signalsContainer.appendChild(signalElement);
    } catch (error) {
        console.error('Error updating signals container:', error);

        // Show error message in container
        const signalsContainer = document.getElementById('signalsContainer');
        if (signalsContainer) {
            signalsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <h4>Error Generating Signal</h4>
                    <p>${error.message}</p>
                </div>
            `;
        }
    }
}

/**
 * Get signal alert class based on signal type
 * @param {string} type - Signal type (buy, sell, neutral)
 * @returns {string} - Alert class
 */
function getSignalAlertClass(type) {
    switch (type) {
        case 'buy':
            return 'success';
        case 'sell':
            return 'danger';
        case 'neutral':
            return 'warning';
        default:
            return 'info';
    }
}

/**
 * Get signal type text based on signal type
 * @param {string} type - Signal type (buy, sell, neutral)
 * @returns {string} - Signal type text
 */
function getSignalTypeText(type) {
    switch (type) {
        case 'buy':
            return 'Buy Signal';
        case 'sell':
            return 'Sell Signal';
        case 'neutral':
            return 'Neutral Signal';
        default:
            return 'Unknown Signal';
    }
}

// Export functions for use in other modules
window.app = {
    loadMarketData,
    updateChart,
    generateTradingSignals,
    appState
};
