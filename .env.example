# Trading Signals App - Environment Variables Example
# Copy this file to .env and replace the values with your own

# Node environment: development, production, test
NODE_ENV=development

# Server port
PORT=3000

# Logging
LOG_LEVEL=debug # debug, info, warn, error

# MongoDB connection
MONGODB_URI=mongodb://localhost:27017/trading-signals

# Redis configuration
USE_REDIS=false # set to true to use Redis
REDIS_URL=redis://localhost:6379 
CACHE_TTL=3600 # Cache TTL in seconds (1 hour)

# CORS configuration
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080

# API Keys for various data providers (replace with your own)
ALPHA_VANTAGE_API_KEY=your_key_here
TWELVE_DATA_API_KEY=your_key_here
FINNHUB_API_KEY=your_key_here
FRED_API_KEY=your_key_here
POLYGON_API_KEY=your_key_here
FMP_API_KEY=your_key_here

# Security
JWT_SECRET=change_this_to_a_secure_random_string
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Optional features
ENABLE_MOCK_DATA=true # Use mock data if API/DB unavailable 