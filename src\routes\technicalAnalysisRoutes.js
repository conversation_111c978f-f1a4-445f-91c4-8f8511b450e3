import express from 'express';
import { protect } from '../middleware/auth.js';
import {
  getTechnicalIndicator,
  getEconomicIndicator,
  getFullAnalysis,
  getTradingSignals,
  getSMCStructures,
} from '../controllers/technicalAnalysisController.js';

const router = express.Router();

// Protect all routes
router.use(protect);

// Technical analysis routes
router.get('/indicator/:indicator', getTechnicalIndicator);
router.get('/economic', getEconomicIndicator);
router.get('/analysis/:symbol', getFullAnalysis);
router.get('/signals/:symbol', getTradingSignals);
router.get('/smc/:symbol', getSMCStructures);

export default router; 