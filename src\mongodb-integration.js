/**
 * MongoDB Integration for Trading Signals App
 *
 * This file provides MongoDB integration for the Trading Signals App.
 */

const express = require('express');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const mongodbService = require('./services/mongodbService');
const repositoryFactory = require('./repositories/RepositoryFactory');
const logger = require('./utils/logger');

// JWT Secret
const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key-should-be-long-and-secure";
const JWT_EXPIRATION = '24h';

/**
 * Initialize MongoDB and add routes to Express app
 * @param {Express} app - Express app instance
 * @param {string} mongoUri - MongoDB connection URI
 * @returns {Promise<Object>} MongoDB client and database
 */
async function setupMongoDB(app, mongoUri) {
  try {
    logger.info('Setting up MongoDB integration');

    // Initialize MongoDB connection
    const { client, db } = await mongodbService.initialize(mongoUri);

    // Create indexes for better performance
    await mongodbService.createIndexes();

    // Add MongoDB routes to Express app
    addMongoDBRoutes(app);

    // Add MongoDB status endpoint
    app.get('/api/mongodb-status', async (req, res) => {
      try {
        const status = await mongodbService.checkStatus();
        res.status(status.status === 'connected' ? 200 : 500).json(status);
      } catch (error) {
        logger.error('Error checking MongoDB status', error);
        res.status(500).json({
          status: 'error',
          message: 'Error checking MongoDB status',
          error: error.message
        });
      }
    });

    logger.info('MongoDB integration setup complete');
    return { client, db };
  } catch (error) {
    logger.error('Error setting up MongoDB integration', error);
    throw error;
  }
}

/**
 * Add MongoDB routes to Express app
 * @param {Express} app - Express app instance
 */
function addMongoDBRoutes(app) {
  // Import middleware
  const validationMiddleware = require('./middleware/validationMiddleware');
  const cacheMiddleware = require('./middleware/cacheMiddleware');
  const validationSchemas = require('./services/validationService').schemas;

  // Authentication middleware
  const authenticateToken = (req, res, next) => {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        status: 'error',
        message: 'Authentication token required'
      });
    }

    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      req.user = decoded;
      next();
    } catch (error) {
      logger.warn('Invalid authentication token', { error: error.message });
      return res.status(403).json({
        status: 'error',
        message: 'Invalid or expired token'
      });
    }
  };

  // User routes

  // Register a new user
  app.post(
    '/api/auth/register',
    validationMiddleware.validateBody(validationSchemas.user),
    cacheMiddleware.clearCache('user_*'),
    async (req, res) => {
      try {
        const userRepo = repositoryFactory.getInstance().getUserRepository();
        const userPrefRepo = repositoryFactory.getInstance().getUserPreferenceRepository();

        // Create user
        const user = await userRepo.createUser(req.body);

        // Create token
        const token = jwt.sign(
          { userId: user._id, email: user.email, username: user.username, role: user.role },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRATION }
        );

        // Create default user preferences
        await userPrefRepo.getOrCreatePreferences(user._id);

        res.status(201).json({
          status: 'success',
          message: 'User registered successfully',
          data: {
            token,
            user: {
              id: user._id,
              email: user.email,
              username: user.username,
              role: user.role
            }
          }
        });
      } catch (error) {
        logger.error('Registration error', error);

        // Handle validation errors
        if (error.validationErrors) {
          return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: error.validationErrors
          });
        }

        // Handle duplicate key errors
        if (error.code === 11000 || error.message.includes('already exists')) {
          return res.status(409).json({
            status: 'error',
            message: 'User with this email or username already exists'
          });
        }

        res.status(400).json({
          status: 'error',
          message: error.message
        });
      }
    }
  );

  // Login a user
  app.post(
    '/api/auth/login',
    validationMiddleware.validateBody(Joi.object({
      email: Joi.string().email().required(),
      password: Joi.string().required()
    })),
    async (req, res) => {
      try {
        const { email, password } = req.body;
        const userRepo = repositoryFactory.getInstance().getUserRepository();

        // Verify credentials
        const user = await userRepo.verifyCredentials(email, password);

        // Create token
        const token = jwt.sign(
          { userId: user._id, email: user.email, username: user.username, role: user.role },
          JWT_SECRET,
          { expiresIn: JWT_EXPIRATION }
        );

        res.status(200).json({
          status: 'success',
          message: 'Login successful',
          data: {
            token,
            user: {
              id: user._id,
              email: user.email,
              username: user.username,
              role: user.role
            }
          }
        });
      } catch (error) {
        logger.error('Login error', error);

        // Handle authentication errors
        if (error.message.includes('Invalid email or password')) {
          return res.status(401).json({
            status: 'error',
            message: 'Invalid email or password'
          });
        }

        res.status(500).json({
          status: 'error',
          message: 'An error occurred during login'
        });
      }
    }
  );

  // Get user profile
  app.get(
    '/api/auth/profile',
    authenticateToken,
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.SHORT,
      req => `user_profile_${req.user.userId}`
    ),
    async (req, res) => {
      try {
        const userRepo = repositoryFactory.getInstance().getUserRepository();
        const userPrefRepo = repositoryFactory.getInstance().getUserPreferenceRepository();

        // Get user
        const user = await userRepo.findById(req.user.userId);

        if (!user) {
          return res.status(404).json({
            status: 'error',
            message: 'User not found'
          });
        }

        // Get user preferences
        const preferences = await userPrefRepo.findByUserId(req.user.userId);

        res.status(200).json({
          status: 'success',
          data: {
            user: {
              id: user._id,
              email: user.email,
              username: user.username,
              role: user.role,
              createdAt: user.createdAt
            },
            preferences: preferences || {}
          }
        });
      } catch (error) {
        logger.error('Profile error', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting user profile',
          error: error.message
        });
      }
    }
  );

  // Update user preferences
  app.put(
    '/api/auth/preferences',
    authenticateToken,
    validationMiddleware.validateBody(validationSchemas.userPreferences),
    cacheMiddleware.clearCache([
      `user_profile_*`,
      `repo_userPreferences_*`
    ]),
    async (req, res) => {
      try {
        const userPrefRepo = repositoryFactory.getInstance().getUserPreferenceRepository();

        // Update preferences
        const result = await userPrefRepo.savePreferences(req.user.userId, req.body);

        res.status(200).json({
          status: 'success',
          message: 'Preferences updated successfully',
          data: {
            preferences: result
          }
        });
      } catch (error) {
        logger.error('Preferences update error', error);

        // Handle validation errors
        if (error.validationErrors) {
          return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: error.validationErrors
          });
        }

        res.status(500).json({
          status: 'error',
          message: 'Error updating preferences',
          error: error.message
        });
      }
    }
  );

  // Trading signals routes

  // Get signals with pagination
  app.get(
    '/api/signals',
    authenticateToken,
    validationMiddleware.validateQuery(Joi.object({
      page: Joi.number().integer().min(1),
      limit: Joi.number().integer().min(1).max(100),
      symbol: Joi.string(),
      type: Joi.string().valid('buy', 'sell'),
      status: Joi.string().valid('active', 'executed', 'expired'),
      timeframe: Joi.string().valid('M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN')
    })),
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.SHORT,
      req => `signals_${req.user.userId}_${JSON.stringify(req.query)}`
    ),
    async (req, res) => {
      try {
        const signalRepo = repositoryFactory.getInstance().getSignalRepository();

        // Build query
        const query = { userId: req.user.userId };

        // Add filters if provided
        if (req.query.symbol) query.symbol = req.query.symbol;
        if (req.query.type) query.type = req.query.type;
        if (req.query.status) query.status = req.query.status;
        if (req.query.timeframe) query.timeframe = req.query.timeframe;

        // Get paginated signals
        const result = await signalRepo.findWithPagination(
          query,
          req.query,
          { sort: { createdAt: -1 } }
        );

        res.status(200).json({
          status: 'success',
          data: result
        });
      } catch (error) {
        logger.error('Error getting signals', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting signals',
          error: error.message
        });
      }
    }
  );

  // Create a signal
  app.post(
    '/api/signals',
    authenticateToken,
    validationMiddleware.validateBody(validationSchemas.signal),
    cacheMiddleware.clearCache([
      `signals_${req.user?.userId}_*`,
      `repo_tradingSignals_*`
    ]),
    async (req, res) => {
      try {
        const signalRepo = repositoryFactory.getInstance().getSignalRepository();

        // Create signal
        const signal = await signalRepo.createSignal({
          ...req.body,
          userId: req.user.userId
        });

        res.status(201).json({
          status: 'success',
          message: 'Signal created successfully',
          data: {
            signal
          }
        });
      } catch (error) {
        logger.error('Error creating signal', error);

        // Handle validation errors
        if (error.validationErrors) {
          return res.status(400).json({
            status: 'error',
            message: 'Validation failed',
            errors: error.validationErrors
          });
        }

        res.status(500).json({
          status: 'error',
          message: 'Error creating signal',
          error: error.message
        });
      }
    }
  );

  // Analytics routes

  // Get signal performance
  app.get(
    '/api/analytics/signal-performance',
    authenticateToken,
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.MEDIUM,
      req => `analytics_signal_performance_${req.user.userId}`
    ),
    async (req, res) => {
      try {
        const analyticsService = require('./services/analyticsService');
        const performance = await analyticsService.getSignalPerformance(req.user.userId);

        res.status(200).json({
          status: 'success',
          data: performance
        });
      } catch (error) {
        logger.error('Error getting signal performance', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting signal performance',
          error: error.message
        });
      }
    }
  );

  // Get signal distribution by symbol
  app.get(
    '/api/analytics/signal-distribution/symbol',
    authenticateToken,
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.MEDIUM,
      req => `analytics_signal_distribution_symbol_${req.user.userId}`
    ),
    async (req, res) => {
      try {
        const analyticsService = require('./services/analyticsService');
        const distribution = await analyticsService.getSignalDistributionBySymbol(req.user.userId);

        res.status(200).json({
          status: 'success',
          data: distribution
        });
      } catch (error) {
        logger.error('Error getting signal distribution by symbol', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting signal distribution by symbol',
          error: error.message
        });
      }
    }
  );

  // Get signal distribution by timeframe
  app.get(
    '/api/analytics/signal-distribution/timeframe',
    authenticateToken,
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.MEDIUM,
      req => `analytics_signal_distribution_timeframe_${req.user.userId}`
    ),
    async (req, res) => {
      try {
        const analyticsService = require('./services/analyticsService');
        const distribution = await analyticsService.getSignalDistributionByTimeframe(req.user.userId);

        res.status(200).json({
          status: 'success',
          data: distribution
        });
      } catch (error) {
        logger.error('Error getting signal distribution by timeframe', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting signal distribution by timeframe',
          error: error.message
        });
      }
    }
  );

  // Get signal trend over time
  app.get(
    '/api/analytics/signal-trend',
    authenticateToken,
    validationMiddleware.validateQuery(Joi.object({
      days: Joi.number().integer().min(1).max(365).default(30)
    })),
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.MEDIUM,
      req => `analytics_signal_trend_${req.user.userId}_${req.query.days || 30}`
    ),
    async (req, res) => {
      try {
        const analyticsService = require('./services/analyticsService');
        const days = parseInt(req.query.days || 30, 10);
        const trend = await analyticsService.getSignalTrendOverTime(req.user.userId, days);

        res.status(200).json({
          status: 'success',
          data: trend
        });
      } catch (error) {
        logger.error('Error getting signal trend', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting signal trend',
          error: error.message
        });
      }
    }
  );

  // Get market data statistics
  app.get(
    '/api/analytics/market-data-stats',
    authenticateToken,
    validationMiddleware.validateQuery(Joi.object({
      symbol: Joi.string().required(),
      timeframe: Joi.string().valid('M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN').required()
    })),
    cacheMiddleware.cacheResponse(
      cacheService.DEFAULT_TTL.SHORT,
      req => `analytics_market_data_stats_${req.query.symbol}_${req.query.timeframe}`
    ),
    async (req, res) => {
      try {
        const analyticsService = require('./services/analyticsService');
        const { symbol, timeframe } = req.query;
        const stats = await analyticsService.getMarketDataStatistics(symbol, timeframe);

        res.status(200).json({
          status: 'success',
          data: stats
        });
      } catch (error) {
        logger.error('Error getting market data statistics', error);
        res.status(500).json({
          status: 'error',
          message: 'Error getting market data statistics',
          error: error.message
        });
      }
    }
  );

  logger.info('MongoDB routes added to Express app');
}

/**
 * Close MongoDB connection
 */
async function closeMongoDB() {
  try {
    await mongodbService.close();
  } catch (error) {
    logger.error('Error closing MongoDB connection', error);
  }
}

module.exports = {
  setupMongoDB,
  closeMongoDB
};
