/**
 * User Authentication with MongoDB for Trading Signals App
 * 
 * This file demonstrates how to implement user authentication using MongoDB.
 */

const { MongoClient, ObjectId } = require('mongodb');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// MongoDB Connection URI - Replace with your actual connection string
const uri = "mongodb://localhost:27017/tradingSignalsApp";

// JWT Secret - In production, store this in environment variables
const JWT_SECRET = "your-secret-key-should-be-long-and-secure";
const JWT_EXPIRATION = '24h';

// Create a MongoDB client
const client = new MongoClient(uri, {
  useNewUrlParser: true,
  useUnifiedTopology: true
});

// Database and collection names
const DB_NAME = "tradingSignalsApp";
const USERS_COLLECTION = "users";

/**
 * Connect to MongoDB
 * @returns {Promise<MongoClient>} MongoDB client instance
 */
async function connectToMongoDB() {
  try {
    await client.connect();
    console.log("Connected to MongoDB successfully");
    return client;
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
    throw error;
  }
}

/**
 * Get the users collection
 * @returns {Collection} MongoDB collection
 */
function getUsersCollection() {
  return client.db(DB_NAME).collection(USERS_COLLECTION);
}

/**
 * Register a new user
 * @param {Object} userData - User registration data
 * @returns {Promise<Object>} Result of the registration
 */
async function registerUser(userData) {
  try {
    const usersCollection = getUsersCollection();
    
    // Check if user already exists
    const existingUser = await usersCollection.findOne({ email: userData.email });
    if (existingUser) {
      throw new Error("User with this email already exists");
    }
    
    // Hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(userData.password, saltRounds);
    
    // Create user object with hashed password
    const userToInsert = {
      username: userData.username,
      email: userData.email,
      password: hashedPassword,
      fullName: userData.fullName,
      role: userData.role || 'user',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert the user
    const result = await usersCollection.insertOne(userToInsert);
    
    // Return user data without password
    const { password, ...userWithoutPassword } = userToInsert;
    return {
      ...userWithoutPassword,
      _id: result.insertedId
    };
  } catch (error) {
    console.error("Error registering user:", error);
    throw error;
  }
}

/**
 * Login a user
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} Login result with JWT token
 */
async function loginUser(email, password) {
  try {
    const usersCollection = getUsersCollection();
    
    // Find the user
    const user = await usersCollection.findOne({ email });
    if (!user) {
      throw new Error("User not found");
    }
    
    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new Error("Invalid password");
    }
    
    // Update last login time
    await usersCollection.updateOne(
      { _id: user._id },
      { $set: { lastLogin: new Date() } }
    );
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user._id,
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRATION }
    );
    
    // Return user data and token (without password)
    const { password: _, ...userWithoutPassword } = user;
    return {
      user: userWithoutPassword,
      token
    };
  } catch (error) {
    console.error("Error logging in:", error);
    throw error;
  }
}

/**
 * Verify JWT token
 * @param {string} token - JWT token
 * @returns {Promise<Object>} Decoded token payload
 */
function verifyToken(token) {
  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    return decoded;
  } catch (error) {
    console.error("Error verifying token:", error);
    throw new Error("Invalid token");
  }
}

/**
 * Get user by ID
 * @param {string} userId - User ID
 * @returns {Promise<Object|null>} User document or null if not found
 */
async function getUserById(userId) {
  try {
    const usersCollection = getUsersCollection();
    const user = await usersCollection.findOne({ _id: new ObjectId(userId) });
    
    if (user) {
      // Remove password from the returned object
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    
    return null;
  } catch (error) {
    console.error("Error getting user by ID:", error);
    throw error;
  }
}

/**
 * Change user password
 * @param {string} userId - User ID
 * @param {string} currentPassword - Current password
 * @param {string} newPassword - New password
 * @returns {Promise<boolean>} Success status
 */
async function changePassword(userId, currentPassword, newPassword) {
  try {
    const usersCollection = getUsersCollection();
    
    // Find the user
    const user = await usersCollection.findOne({ _id: new ObjectId(userId) });
    if (!user) {
      throw new Error("User not found");
    }
    
    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isPasswordValid) {
      throw new Error("Current password is incorrect");
    }
    
    // Hash the new password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    // Update the password
    await usersCollection.updateOne(
      { _id: new ObjectId(userId) },
      { 
        $set: { 
          password: hashedPassword,
          updatedAt: new Date()
        } 
      }
    );
    
    return true;
  } catch (error) {
    console.error("Error changing password:", error);
    throw error;
  }
}

/**
 * Close MongoDB connection
 */
async function closeConnection() {
  try {
    await client.close();
    console.log("MongoDB connection closed");
  } catch (error) {
    console.error("Error closing MongoDB connection:", error);
  }
}

// Export all functions
module.exports = {
  connectToMongoDB,
  registerUser,
  loginUser,
  verifyToken,
  getUserById,
  changePassword,
  closeConnection
};
