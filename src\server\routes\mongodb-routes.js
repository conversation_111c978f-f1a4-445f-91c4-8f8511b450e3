/**
 * MongoDB Routes for Trading Signals App
 * 
 * This file contains Express routes for MongoDB integration.
 * Import and use these routes in your server.js file.
 */

const express = require('express');
const router = express.Router();
const authMongoDB = require('./auth-mongodb');
const db = require('./db');

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
  
  if (!token) {
    return res.status(401).json({ message: 'Authentication token required' });
  }
  
  try {
    const decoded = authMongoDB.verifyToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    return res.status(403).json({ message: 'Invalid or expired token' });
  }
};

// Connect to MongoDB when routes are loaded
let dbClient;
(async () => {
  try {
    dbClient = await db.connect();
    console.log('MongoDB routes connected to database');
    
    // Create indexes for better performance
    await db.createIndexes();
  } catch (error) {
    console.error('Failed to connect MongoDB routes to database:', error);
  }
})();

// Authentication routes

// Register a new user
router.post('/auth/register', async (req, res) => {
  try {
    const userData = req.body;
    const newUser = await authMongoDB.registerUser(userData);
    res.status(201).json({ message: 'User registered successfully', user: newUser });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Login a user
router.post('/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;
    const result = await authMongoDB.loginUser(email, password);
    res.status(200).json(result);
  } catch (error) {
    res.status(401).json({ message: error.message });
  }
});

// Get current user
router.get('/auth/me', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    const user = await authMongoDB.getUserById(userId);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    res.status(200).json(user);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Change password
router.post('/auth/change-password', authenticateToken, async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.userId;
    
    await authMongoDB.changePassword(userId, currentPassword, newPassword);
    res.status(200).json({ message: 'Password changed successfully' });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Trading signals routes

// Save a trading signal
router.post('/signals', authenticateToken, async (req, res) => {
  try {
    const signalsCollection = db.getCollection(db.collections.SIGNALS);
    
    const signalData = {
      ...req.body,
      userId: req.user.userId, // Associate signal with the user who created it
      createdAt: new Date(),
      status: "active" // active, expired, executed
    };
    
    const result = await signalsCollection.insertOne(signalData);
    res.status(201).json({ 
      message: 'Trading signal created successfully',
      signalId: result.insertedId
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get trading signals
router.get('/signals', authenticateToken, async (req, res) => {
  try {
    const signalsCollection = db.getCollection(db.collections.SIGNALS);
    
    // Extract query parameters for filtering
    const { symbol, type, timeframe } = req.query;
    
    // Build filter object based on provided parameters
    const filter = { userId: req.user.userId }; // Only get signals for the current user
    if (symbol) filter.symbol = symbol;
    if (type) filter.type = type;
    if (timeframe) filter.timeframe = timeframe;
    
    const signals = await signalsCollection.find(filter)
      .sort({ createdAt: -1 })
      .toArray();
      
    res.status(200).json(signals);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get a specific signal
router.get('/signals/:id', authenticateToken, async (req, res) => {
  try {
    const signalsCollection = db.getCollection(db.collections.SIGNALS);
    const signalId = req.params.id;
    
    const signal = await signalsCollection.findOne({ 
      _id: new db.ObjectId(signalId),
      userId: req.user.userId // Ensure the signal belongs to the user
    });
    
    if (!signal) {
      return res.status(404).json({ message: 'Signal not found' });
    }
    
    res.status(200).json(signal);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Update a signal
router.put('/signals/:id', authenticateToken, async (req, res) => {
  try {
    const signalsCollection = db.getCollection(db.collections.SIGNALS);
    const signalId = req.params.id;
    
    // Ensure the signal exists and belongs to the user
    const existingSignal = await signalsCollection.findOne({ 
      _id: new db.ObjectId(signalId),
      userId: req.user.userId
    });
    
    if (!existingSignal) {
      return res.status(404).json({ message: 'Signal not found' });
    }
    
    // Update the signal
    const updateData = {
      ...req.body,
      updatedAt: new Date()
    };
    
    // Don't allow changing the userId
    delete updateData.userId;
    delete updateData._id;
    
    const result = await signalsCollection.updateOne(
      { _id: new db.ObjectId(signalId) },
      { $set: updateData }
    );
    
    res.status(200).json({ 
      message: 'Signal updated successfully',
      modifiedCount: result.modifiedCount
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Delete a signal
router.delete('/signals/:id', authenticateToken, async (req, res) => {
  try {
    const signalsCollection = db.getCollection(db.collections.SIGNALS);
    const signalId = req.params.id;
    
    // Ensure the signal exists and belongs to the user
    const existingSignal = await signalsCollection.findOne({ 
      _id: new db.ObjectId(signalId),
      userId: req.user.userId
    });
    
    if (!existingSignal) {
      return res.status(404).json({ message: 'Signal not found' });
    }
    
    // Delete the signal
    const result = await signalsCollection.deleteOne({ _id: new db.ObjectId(signalId) });
    
    res.status(200).json({ 
      message: 'Signal deleted successfully',
      deletedCount: result.deletedCount
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// User preferences routes

// Save user preferences
router.post('/preferences', authenticateToken, async (req, res) => {
  try {
    const preferencesCollection = db.getCollection(db.collections.USER_PREFERENCES);
    const userId = req.user.userId;
    
    // Check if preferences already exist for this user
    const existingPrefs = await preferencesCollection.findOne({ 
      userId: userId
    });
    
    if (existingPrefs) {
      // Update existing preferences
      const result = await preferencesCollection.updateOne(
        { userId: userId },
        { 
          $set: { 
            ...req.body,
            updatedAt: new Date() 
          } 
        }
      );
      
      res.status(200).json({ 
        message: 'Preferences updated successfully',
        modifiedCount: result.modifiedCount
      });
    } else {
      // Create new preferences
      const prefsToInsert = {
        userId: userId,
        ...req.body,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      const result = await preferencesCollection.insertOne(prefsToInsert);
      
      res.status(201).json({ 
        message: 'Preferences created successfully',
        preferencesId: result.insertedId
      });
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get user preferences
router.get('/preferences', authenticateToken, async (req, res) => {
  try {
    const preferencesCollection = db.getCollection(db.collections.USER_PREFERENCES);
    const userId = req.user.userId;
    
    const preferences = await preferencesCollection.findOne({ userId: userId });
    
    if (!preferences) {
      return res.status(404).json({ message: 'Preferences not found' });
    }
    
    res.status(200).json(preferences);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
