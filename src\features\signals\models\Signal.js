import mongoose from 'mongoose';

const signalSchema = new mongoose.Schema({
  symbol: { type: String, required: true },
  type: { type: String, enum: ['buy', 'sell'], required: true },
  strength: { type: Number, min: 0, max: 100, required: true },
  timeframe: { type: String, required: true },
  createdAt: { type: Date, default: Date.now },
  generatedBy: { type: String, default: 'system' }, // or 'ai', 'user', etc.
  notes: { type: String }
});

const Signal = mongoose.model('Signal', signalSchema);
export default Signal; 