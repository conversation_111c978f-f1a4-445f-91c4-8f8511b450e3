/**
 * API Fallback Handler
 * 
 * This module implements a cascading fallback mechanism for API requests.
 * If the primary API fails, it automatically tries alternative APIs in order of preference.
 */

const logger = require('../logging');
const config = require('../config');
const mockData = require('./mockData');

// Import all API clients
let apiClients = {};

// Dynamically load API clients if possible
try {
  apiClients = {
    alphaVantage: require('../api-clients/alphaVantage'),
    twelveData: require('../api-clients/twelveData'),
    finnhub: require('../api-clients/finnhub'),
    fred: require('../api-clients/fred'),
    polygon: require('../api-clients/polygon'),
    fmp: require('../api-clients/fmp')
  };
} catch (error) {
  logger.warn('Some API clients could not be loaded:', error.message);
}

/**
 * Execute an API request with fallback support
 * @param {Array} apiOrder - Priority order of APIs to try
 * @param {Object} params - Parameters for the API request
 * @returns {Promise<Object>} - API response data
 */
async function fallbackAPIs(apiOrder, params) {
  if (!apiOrder || !Array.isArray(apiOrder) || apiOrder.length === 0) {
    logger.error('Invalid API order provided');
    throw new Error('Invalid API configuration');
  }

  const errors = [];
  
  // Try each API in order until one succeeds
  for (const apiName of apiOrder) {
    try {
      // Check if API client exists
      if (!apiClients[apiName]) {
        logger.warn(`API client ${apiName} not found, skipping`);
        errors.push(`${apiName}: API client not found`);
        continue;
      }
      
      logger.info(`Attempting to fetch data from ${apiName} for ${params.symbol}`);
      
      // Call the API
      const result = await apiClients[apiName].getData(params);
      
      if (result && (!result.data || result.data.length === 0)) {
        logger.warn(`${apiName} returned empty data for ${params.symbol}`);
        errors.push(`${apiName}: Empty data returned`);
        continue;
      }
      
      logger.info(`Successfully fetched data from ${apiName} for ${params.symbol}`);
      
      // Add source information to the result
      return {
        ...result,
        source: apiName,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error(`Error fetching data from ${apiName}:`, error.message);
      errors.push(`${apiName}: ${error.message}`);
      // Continue to next API
    }
  }
  
  // If all APIs failed, try to use mock data if enabled
  if (process.env.ENABLE_MOCK_DATA === 'true') {
    logger.warn(`All APIs failed for ${params.symbol}, using mock data`);
    
    // Generate mock data based on the request parameters
    return {
      data: mockData.generateMockData(params),
      source: 'mock',
      timestamp: new Date().toISOString(),
      mockData: true
    };
  }
  
  // If mock data is disabled, throw an error with all the failures
  logger.error(`All APIs failed for ${params.symbol}`);
  throw new Error(`Failed to fetch data from all available sources: ${errors.join('; ')}`);
}

module.exports = fallbackAPIs; 