/* Base styles for Trading Signals App */

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f5f5f5;
}

a {
  color: #0d6efd;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 1rem;
  line-height: 1.2;
}

h1 {
  font-size: 2.5rem;
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.75rem;
}

p {
  margin-bottom: 1rem;
}

/* Navigation */
.main-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo a {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-links a {
  color: #6c757d;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: #0d6efd;
}

.nav-links a.active {
  color: #0d6efd;
  font-weight: 600;
}

.auth-nav {
  display: flex;
  gap: 1rem;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #0d6efd;
  color: white;
  border: none;
}

.btn-primary:hover {
  background-color: #0b5ed7;
}

.btn-outline {
  background-color: transparent;
  color: #0d6efd;
  border: 1px solid #0d6efd;
}

.btn-outline:hover {
  background-color: #0d6efd;
  color: white;
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #0d6efd;
  outline: none;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Messages */
.message {
  margin: 1rem 0;
  padding: 0.75rem;
  border-radius: 4px;
}

.message.success {
  background-color: #d1e7dd;
  color: #0f5132;
}

.message.error {
  background-color: #f8d7da;
  color: #842029;
}

/* Footer */
footer {
  text-align: center;
  padding: 2rem;
  margin-top: 2rem;
  background-color: #f8f9fa;
  border-top: 1px solid #dee2e6;
}

/* Chart styles */
.chart-container {
  width: 100%;
  height: 400px;
  position: relative;
  margin-bottom: 1rem;
}

canvas#priceChart {
  width: 100% !important;
  height: 100% !important;
}

/* Trading signals */
.signal-card {
  border-left: 4px solid #dee2e6;
  padding: 15px;
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.signal-card.buy {
  border-left-color: #28a745;
}

.signal-card.sell {
  border-left-color: #dc3545;
}

.signal-card.neutral {
  border-left-color: #ffc107;
}

/* Responsive styles */
@media (max-width: 768px) {
  .main-nav {
    flex-direction: column;
    padding: 1rem;
  }

  .nav-links {
    margin: 1rem 0;
  }

  .auth-nav {
    width: 100%;
    justify-content: center;
  }

  .dashboard-container {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 300px;
  }
}

/* Dark mode (optional) */
@media (prefers-color-scheme: dark) {
  body {
    background-color: #121212;
    color: #e0e0e0;
  }

  .main-nav {
    background-color: #1e1e1e;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .logo a {
    color: #e0e0e0;
  }

  .nav-links a {
    color: #adb5bd;
  }

  .nav-links a:hover,
  .nav-links a.active {
    color: #6ea8fe;
  }

  .dashboard-card,
  .form-container,
  .signal-card {
    background-color: #1e1e1e;
  }

  .stat-card,
  .signal-header,
  .welcome-message,
  .filter-container {
    background-color: #2c2c2c;
  }

  .signal-actions,
  footer {
    background-color: #252525;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    background-color: #2c2c2c;
    border-color: #444;
    color: #e0e0e0;
  }

  .btn-outline {
    color: #6ea8fe;
    border-color: #6ea8fe;
  }

  .btn-primary {
    background-color: #0d6efd;
  }

  .btn-primary:hover {
    background-color: #0a58ca;
  }
}
