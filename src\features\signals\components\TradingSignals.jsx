import React, { useState, useEffect } from 'react';
import { alphaVantageAPI, finnhubAPI } from '../../../services/apiService.js';

/**
 * TradingSignals Component
 * 
 * Displays trading signals with filtering options and detailed analysis
 */
const TradingSignals = () => {
  // State for filters
  const [filters, setFilters] = useState({
    market: 'forex',
    symbol: 'EURUSD',
    timeframe: 'H1',
    signalType: 'all'
  });
  
  // State for signals
  const [signals, setSignals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  // State for selected signal details
  const [selectedSignal, setSelectedSignal] = useState(null);
  
  // Available options for filters
  const marketOptions = [
    { value: 'forex', label: 'Forex' },
    { value: 'commodities', label: 'Commodities' },
    { value: 'indices', label: 'Indices' },
    { value: 'crypto', label: 'Cryptocurrencies' }
  ];
  
  const symbolOptions = {
    forex: [
      { value: 'EURUSD', label: 'EUR/USD' },
      { value: 'GBPUSD', label: 'GBP/USD' },
      { value: 'USDJPY', label: 'USD/JPY' },
      { value: 'AUDUSD', label: 'AUD/USD' }
    ],
    commodities: [
      { value: 'XAUUSD', label: 'Gold (XAU/USD)' },
      { value: 'XAGUSD', label: 'Silver (XAG/USD)' },
      { value: 'WTIUSD', label: 'Crude Oil (WTI)' }
    ],
    indices: [
      { value: 'SPX', label: 'S&P 500' },
      { value: 'NDX', label: 'NASDAQ 100' },
      { value: 'DJI', label: 'Dow Jones' }
    ],
    crypto: [
      { value: 'BTCUSD', label: 'Bitcoin (BTC/USD)' },
      { value: 'ETHUSD', label: 'Ethereum (ETH/USD)' }
    ]
  };
  
  const timeframeOptions = [
    { value: 'M5', label: '5 Minutes' },
    { value: 'M15', label: '15 Minutes' },
    { value: 'M30', label: '30 Minutes' },
    { value: 'H1', label: '1 Hour' },
    { value: 'H4', label: '4 Hours' },
    { value: 'D1', label: 'Daily' }
  ];
  
  const signalTypeOptions = [
    { value: 'all', label: 'All Signals' },
    { value: 'buy', label: 'Buy Signals' },
    { value: 'sell', label: 'Sell Signals' }
  ];
  
  // Handle filter changes
  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    
    // If market changes, reset symbol to first option of that market
    if (name === 'market') {
      setFilters({
        ...filters,
        market: value,
        symbol: symbolOptions[value][0].value
      });
    } else {
      setFilters({
        ...filters,
        [name]: value
      });
    }
  };
  
  // Apply filters and fetch signals
  const applyFilters = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real app, this would be an API call to your backend
      // For now, we'll generate mock signals based on the filters
      await fetchSignals(filters);
    } catch (err) {
      console.error('Error fetching signals:', err);
      setError('Failed to load trading signals. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch signals based on filters
  const fetchSignals = async (filterParams) => {
    // This would be replaced with an actual API call
    // For now, we'll generate mock signals
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800));
    
    // Generate mock signals
    const mockSignals = [];
    
    // Number of signals to generate
    const signalCount = Math.floor(Math.random() * 5) + 3; // 3-7 signals
    
    for (let i = 0; i < signalCount; i++) {
      const isBuy = Math.random() > 0.5;
      
      // Skip if filtering by signal type
      if (filterParams.signalType === 'buy' && !isBuy) continue;
      if (filterParams.signalType === 'sell' && isBuy) continue;
      
      // Generate random prices
      const basePrice = filterParams.symbol === 'BTCUSD' ? 35000 + Math.random() * 5000 :
                        filterParams.symbol === 'ETHUSD' ? 2000 + Math.random() * 300 :
                        filterParams.symbol === 'XAUUSD' ? 1900 + Math.random() * 100 :
                        filterParams.symbol.includes('JPY') ? 140 + Math.random() * 10 :
                        1 + Math.random() * 0.2;
      
      const entryPrice = parseFloat(basePrice.toFixed(4));
      const stopLoss = isBuy ? 
        parseFloat((entryPrice * 0.995).toFixed(4)) : 
        parseFloat((entryPrice * 1.005).toFixed(4));
      const takeProfit = isBuy ? 
        parseFloat((entryPrice * 1.01).toFixed(4)) : 
        parseFloat((entryPrice * 0.99).toFixed(4));
      
      // Generate random confidence
      const confidence = Math.floor(Math.random() * 30) + 70; // 70-99%
      
      // Generate random timestamp within the last 24 hours
      const timestamp = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000).toISOString();
      
      // Generate random indicators that triggered the signal
      const indicators = [];
      const possibleIndicators = [
        'RSI', 'MACD', 'Moving Average Crossover', 'Bollinger Bands', 
        'Stochastic', 'Fibonacci Retracement', 'Ichimoku Cloud'
      ];
      
      // Add 2-4 random indicators
      const indicatorCount = Math.floor(Math.random() * 3) + 2;
      for (let j = 0; j < indicatorCount; j++) {
        const randomIndex = Math.floor(Math.random() * possibleIndicators.length);
        const indicator = possibleIndicators[randomIndex];
        if (!indicators.includes(indicator)) {
          indicators.push(indicator);
        }
      }
      
      mockSignals.push({
        id: i + 1,
        symbol: filterParams.symbol,
        market: filterParams.market,
        timeframe: filterParams.timeframe,
        type: isBuy ? 'BUY' : 'SELL',
        entryPrice,
        stopLoss,
        takeProfit,
        riskReward: parseFloat((Math.abs(takeProfit - entryPrice) / Math.abs(stopLoss - entryPrice)).toFixed(2)),
        confidence,
        timestamp,
        indicators,
        analysis: `${isBuy ? 'Bullish' : 'Bearish'} momentum detected on ${filterParams.timeframe} timeframe. ${indicators.join(', ')} showing strong ${isBuy ? 'buying' : 'selling'} pressure.`
      });
    }
    
    setSignals(mockSignals);
  };
  
  // View signal details
  const viewSignalDetails = (signal) => {
    setSelectedSignal(signal);
  };
  
  // Close signal details modal
  const closeSignalDetails = () => {
    setSelectedSignal(null);
  };
  
  // Apply filters on component mount and when filters change
  useEffect(() => {
    applyFilters();
  }, []);

  return (
    <div className="trading-signals p-4">
      <h1 className="text-2xl font-bold mb-6">Trading Signals</h1>
      
      {/* Filters Section */}
      <section className="mb-8">
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Filters</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Market Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Market
              </label>
              <select
                name="market"
                value={filters.market}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {marketOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            {/* Symbol Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Symbol
              </label>
              <select
                name="symbol"
                value={filters.symbol}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {symbolOptions[filters.market].map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            {/* Timeframe Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Timeframe
              </label>
              <select
                name="timeframe"
                value={filters.timeframe}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {timeframeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
            
            {/* Signal Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Signal Type
              </label>
              <select
                name="signalType"
                value={filters.signalType}
                onChange={handleFilterChange}
                className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              >
                {signalTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="mt-4">
            <button
              onClick={applyFilters}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </section>
      
      {/* Signals Section */}
      <section>
        <h2 className="text-lg font-semibold mb-4">Signals</h2>
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : signals.length === 0 ? (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">No signals found for the selected filters.</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {signals.map(signal => (
              <div 
                key={signal.id} 
                className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                onClick={() => viewSignalDetails(signal)}
              >
                <div className={`p-2 text-white text-center ${signal.type === 'BUY' ? 'bg-green-600' : 'bg-red-600'}`}>
                  {signal.type} {signal.symbol} ({signal.timeframe})
                </div>
                <div className="p-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Entry Price:</span>
                    <span className="font-medium">{signal.entryPrice}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Stop Loss:</span>
                    <span className="font-medium text-red-500">{signal.stopLoss}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Take Profit:</span>
                    <span className="font-medium text-green-500">{signal.takeProfit}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Risk/Reward:</span>
                    <span className="font-medium">{signal.riskReward}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-500 dark:text-gray-400">Confidence:</span>
                    <span className="font-medium">{signal.confidence}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">Time:</span>
                    <span className="font-medium">{new Date(signal.timestamp).toLocaleString()}</span>
                  </div>
                </div>
                <div className="p-2 bg-gray-100 dark:bg-gray-700 text-center">
                  <button className="text-blue-600 dark:text-blue-400 hover:underline">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
      
      {/* Signal Details Modal */}
      {selectedSignal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className={`p-4 text-white ${selectedSignal.type === 'BUY' ? 'bg-green-600' : 'bg-red-600'}`}>
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-bold">
                  {selectedSignal.type} {selectedSignal.symbol} ({selectedSignal.timeframe})
                </h3>
                <button 
                  onClick={closeSignalDetails}
                  className="text-white hover:text-gray-200"
                >
                  &times;
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Entry Price</h4>
                  <p className="text-lg font-medium">{selectedSignal.entryPrice}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Confidence</h4>
                  <p className="text-lg font-medium">{selectedSignal.confidence}%</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Stop Loss</h4>
                  <p className="text-lg font-medium text-red-500">{selectedSignal.stopLoss}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Take Profit</h4>
                  <p className="text-lg font-medium text-green-500">{selectedSignal.takeProfit}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Risk/Reward Ratio</h4>
                  <p className="text-lg font-medium">{selectedSignal.riskReward}</p>
                </div>
                <div>
                  <h4 className="text-sm text-gray-500 dark:text-gray-400">Time</h4>
                  <p className="text-lg font-medium">{new Date(selectedSignal.timestamp).toLocaleString()}</p>
                </div>
              </div>
              
              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Technical Indicators</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedSignal.indicators.map((indicator, index) => (
                    <span 
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded text-sm"
                    >
                      {indicator}
                    </span>
                  ))}
                </div>
              </div>
              
              <div className="mb-4">
                <h4 className="text-sm text-gray-500 dark:text-gray-400 mb-1">Analysis</h4>
                <p className="text-gray-700 dark:text-gray-300">{selectedSignal.analysis}</p>
              </div>
              
              <div className="flex justify-end space-x-2">
                <button 
                  onClick={closeSignalDetails}
                  className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradingSignals;
