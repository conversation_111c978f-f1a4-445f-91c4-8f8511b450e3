import signalsService from '../services/signalsService.js';
import { z } from 'zod';
import { APIError } from '../../../middleware/errorHandler.js';
import notificationService from '../../../services/notificationService.js';

const signalSchema = z.object({
  symbol: z.string().min(3),
  type: z.enum(['buy', 'sell']),
  strength: z.number().min(0).max(100),
  timeframe: z.string().min(1),
  generatedBy: z.string().optional(),
  notes: z.string().optional(),
});

export const getAllSignals = async (req, res, next) => {
  try {
    const signals = await signalsService.getAllSignals();
    res.json({ status: 'success', data: signals });
  } catch (error) {
    next(error);
  }
};

export const createSignal = async (req, res, next) => {
  try {
    const validated = signalSchema.safeParse(req.body);
    if (!validated.success) {
      return next(new APIError(400, 'Invalid signal data'));
    }
    const signal = await signalsService.createSignal(validated.data);
    // Create notification for the user who created the signal
    if (req.user && req.user.id) {
      await notificationService.createNotification({
        user: req.user.id,
        type: 'signal',
        message: `New ${signal.type} signal for ${signal.symbol} (${signal.timeframe}) created.`,
        link: `/signals/${signal._id}`,
      });
    }
    res.status(201).json({ status: 'success', data: signal });
  } catch (error) {
    next(error);
  }
};

export const getSignalById = async (req, res, next) => {
  try {
    const signal = await signalsService.getSignalById(req.params.id);
    if (!signal) return res.status(404).json({ error: 'Signal not found' });
    res.json({ status: 'success', data: signal });
  } catch (error) {
    next(error);
  }
};

export const deleteSignal = async (req, res, next) => {
  try {
    await signalsService.deleteSignal(req.params.id);
    res.json({ status: 'success', message: 'Signal deleted' });
  } catch (error) {
    next(error);
  }
};

export default {
  getAllSignals,
  createSignal,
  getSignalById,
  deleteSignal,
};
