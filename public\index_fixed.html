<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Signals App</title>
    <link rel="stylesheet" href="/css/basic.css">
    <style>
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .btn-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success">
            <strong>Success!</strong> Your application is now running correctly after restructuring.
        </div>
        <h1>Trading Signals App</h1>
        <p>This is a simple test page to verify that the server is working correctly after the project restructuring.</p>
        <p>If you're seeing this page, it means:</p>
        <ul>
            <li>The server is running correctly</li>
            <li>Static files are being served from the correct locations</li>
            <li>The HTML paths have been updated properly</li>
        </ul>
        <p>You can now continue working with your application.</p>
        <div class="text-center btn-group">
            <a href="/index_en.html" class="btn">Go to Main Application</a>
            <a href="/dashboard.html" class="btn btn-secondary">Go to Dashboard</a>
            <a href="/server-info.html" class="btn btn-secondary">Server Information</a>
        </div>
        <div class="alert alert-info mt-20">
            <strong>Note:</strong> Some files might still be missing or have incorrect paths. If you encounter issues, check the server console for 404 errors and fix the paths accordingly.
        </div>
    </div>
</body>
</html>
