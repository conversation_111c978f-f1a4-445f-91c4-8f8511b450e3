/**
 * Enhanced API Service for Trading Signals App
 * 
 * This service extends the existing API service with:
 * - Advanced rate limit handling with retry queues
 * - Improved caching with MongoDB storage
 * - WebSocket/webhook support for real-time data
 * - Extended parameter customization
 */

import axios from 'axios';
import config from '../config/config.js';
import logger from '../utils/logger.js';
import { APIError } from '../middleware/errorHandler.js';
import enhancedCache from '../utils/cache.js';
import RetryQueue from '../utils/retryQueue.js';
import { EventEmitter } from 'events';

class EnhancedAPIService extends EventEmitter {
  /**
   * Create a new EnhancedAPIService instance
   * @param {Object} options - Configuration options
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableRetryQueue: true,
      enableWebhooks: true,
      enableMongoDBCache: true,
      defaultCacheTTL: 300, // 5 minutes
      ...options
    };
    
    // Initialize API providers
    this.providers = {};
    this.initializeProviders();
    
    // Initialize retry queue
    this.retryQueue = new RetryQueue({
      maxConcurrent: 5,
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 30000,
      logger: (msg) => logger.debug(`RetryQueue: ${msg}`)
    });
    
    // Initialize stats
    this.stats = {
      requests: 0,
      cacheHits: 0,
      errors: 0,
      webhookEvents: 0,
      lastRequest: null
    };
    
    logger.info('Enhanced API Service initialized');
  }
  
  /**
   * Initialize API providers
   */
  initializeProviders() {
    // Alpha Vantage
    this.providers.alphaVantage = {
      baseURL: config.api.alphaVantage.baseUrl,
      apiKeys: Array.isArray(config.api.alphaVantage.apiKey) 
        ? config.api.alphaVantage.apiKey 
        : [config.api.alphaVantage.apiKey],
      currentKeyIndex: 0,
      rateLimited: false,
      rateLimitResetTime: null,
      instance: axios.create({
        baseURL: config.api.alphaVantage.baseUrl,
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Trading Signals App/1.0'
        }
      })
    };
    
    // Polygon
    this.providers.polygon = {
      baseURL: config.api.polygon.baseUrl,
      apiKeys: Array.isArray(config.api.polygon.apiKey) 
        ? config.api.polygon.apiKey 
        : [config.api.polygon.apiKey],
      currentKeyIndex: 0,
      rateLimited: false,
      rateLimitResetTime: null,
      instance: axios.create({
        baseURL: config.api.polygon.baseUrl,
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Trading Signals App/1.0'
        }
      })
    };
    
    // FRED
    this.providers.fred = {
      baseURL: config.api.fred.baseUrl,
      apiKeys: Array.isArray(config.api.fred.apiKey) 
        ? config.api.fred.apiKey 
        : [config.api.fred.apiKey],
      currentKeyIndex: 0,
      rateLimited: false,
      rateLimitResetTime: null,
      instance: axios.create({
        baseURL: config.api.fred.baseUrl,
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Trading Signals App/1.0'
        }
      })
    };
    
    // Add more providers as needed
  }
  
  /**
   * Get the next API key for a provider
   * @param {string} provider - Provider name
   * @returns {string} API key
   */
  getNextApiKey(provider) {
    const providerConfig = this.providers[provider];
    if (!providerConfig) {
      throw new Error(`Unknown provider: ${provider}`);
    }
    
    // Rotate to next key
    providerConfig.currentKeyIndex = (providerConfig.currentKeyIndex + 1) % providerConfig.apiKeys.length;
    return providerConfig.apiKeys[providerConfig.currentKeyIndex];
  }
  
  /**
   * Mark a provider as rate limited
   * @param {string} provider - Provider name
   * @param {number} resetTimeMs - Time in ms until rate limit resets
   */
  markRateLimited(provider, resetTimeMs = 60000) {
    const providerConfig = this.providers[provider];
    if (!providerConfig) {
      return;
    }
    
    providerConfig.rateLimited = true;
    providerConfig.rateLimitResetTime = Date.now() + resetTimeMs;
    
    logger.warn(`Provider ${provider} rate limited until ${new Date(providerConfig.rateLimitResetTime).toISOString()}`);
    
    // Emit rate limit event
    this.emit('rateLimited', {
      provider,
      resetTime: providerConfig.rateLimitResetTime,
      resetTimeMs
    });
    
    // Auto-reset after timeout
    setTimeout(() => {
      providerConfig.rateLimited = false;
      providerConfig.rateLimitResetTime = null;
      logger.info(`Provider ${provider} rate limit reset`);
      this.emit('rateLimitReset', { provider });
    }, resetTimeMs);
  }
  
  /**
   * Check if a provider is rate limited
   * @param {string} provider - Provider name
   * @returns {boolean} True if rate limited
   */
  isRateLimited(provider) {
    const providerConfig = this.providers[provider];
    if (!providerConfig) {
      return false;
    }
    
    if (!providerConfig.rateLimited) {
      return false;
    }
    
    // Check if rate limit has expired
    if (providerConfig.rateLimitResetTime && Date.now() > providerConfig.rateLimitResetTime) {
      providerConfig.rateLimited = false;
      providerConfig.rateLimitResetTime = null;
      return false;
    }
    
    return true;
  }
  
  /**
   * Fetch data with retry queue
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async fetchWithRetryQueue(options) {
    const {
      provider,
      endpoint,
      params = {},
      method = 'GET',
      priority = 5,
      useCache = true,
      cacheTTL = this.options.defaultCacheTTL
    } = options;
    
    // Generate cache key
    const cacheKey = this.generateCacheKey(provider, endpoint, params);
    
    // Check cache if enabled
    if (useCache) {
      const cachedData = await this.getCachedData(cacheKey);
      if (cachedData) {
        this.stats.cacheHits++;
        return cachedData;
      }
    }
    
    // Create a promise that will be resolved by the retry queue
    return new Promise((resolve, reject) => {
      this.retryQueue.add({
        provider,
        priority,
        execute: async () => {
          try {
            // Check if provider is rate limited
            if (this.isRateLimited(provider)) {
              throw new Error(`Provider ${provider} is currently rate limited`);
            }
            
            // Get provider config
            const providerConfig = this.providers[provider];
            if (!providerConfig) {
              throw new Error(`Unknown provider: ${provider}`);
            }
            
            // Get current API key
            const apiKey = providerConfig.apiKeys[providerConfig.currentKeyIndex];
            
            // Prepare request
            const requestParams = { ...params };
            
            // Add API key to params for providers that use query param auth
            if (['alphaVantage', 'fred'].includes(provider)) {
              requestParams.apikey = apiKey;
            }
            
            // Make request
            const response = await providerConfig.instance({
              method,
              url: endpoint,
              params: requestParams,
              headers: provider === 'polygon' ? { 'Authorization': `Bearer ${apiKey}` } : {}
            });
            
            // Check for rate limit in response
            if (this.isRateLimitResponse(provider, response.data)) {
              this.markRateLimited(provider);
              throw new Error(`Rate limit reached for provider ${provider}`);
            }
            
            // Cache successful response
            if (useCache) {
              await this.cacheData(cacheKey, response.data, cacheTTL);
            }
            
            // Emit data event
            this.emit('data', {
              provider,
              endpoint,
              params,
              data: response.data
            });
            
            return response.data;
          } catch (error) {
            // Check if this is a rate limit error
            if (this.isRateLimitError(provider, error)) {
              // Try next API key
              this.getNextApiKey(provider);
              
              // If we've tried all keys, mark provider as rate limited
              if (providerConfig.currentKeyIndex === 0) {
                this.markRateLimited(provider);
              }
            }
            
            throw error;
          }
        },
        onSuccess: (data) => {
          resolve(data);
        },
        onError: (error) => {
          this.stats.errors++;
          reject(error);
        }
      });
    });
  }
  
  /**
   * Check if a response indicates rate limiting
   * @param {string} provider - Provider name
   * @param {Object} data - Response data
   * @returns {boolean} True if rate limited
   */
  isRateLimitResponse(provider, data) {
    if (!data) return false;
    
    switch (provider) {
      case 'alphaVantage':
        return data.Note && data.Note.includes('API call frequency');
      case 'polygon':
        return data.status === 429 || (data.error && data.error.includes('rate limit'));
      case 'fred':
        return data.error_code === 429 || (data.error_message && data.error_message.includes('limit'));
      default:
        return false;
    }
  }
  
  /**
   * Check if an error indicates rate limiting
   * @param {string} provider - Provider name
   * @param {Error} error - Error object
   * @returns {boolean} True if rate limited
   */
  isRateLimitError(provider, error) {
    if (!error) return false;
    
    // Check response status
    if (error.response && error.response.status === 429) {
      return true;
    }
    
    // Check error message
    const message = error.message.toLowerCase();
    return (
      message.includes('rate limit') ||
      message.includes('too many requests') ||
      message.includes('api call frequency') ||
      message.includes('429')
    );
  }
  
  /**
   * Generate cache key
   * @param {string} provider - Provider name
   * @param {string} endpoint - API endpoint
   * @param {Object} params - Request parameters
   * @returns {string} Cache key
   */
  generateCacheKey(provider, endpoint, params) {
    return `${provider}:${endpoint}:${JSON.stringify(params)}`;
  }
  
  /**
   * Get cached data
   * @param {string} key - Cache key
   * @returns {Promise<Object|null>} Cached data or null
   */
  async getCachedData(key) {
    // Implementation will depend on whether MongoDB cache is enabled
    // For now, use enhancedCache
    return enhancedCache.get(key);
  }
  
  /**
   * Cache data
   * @param {string} key - Cache key
   * @param {Object} data - Data to cache
   * @param {number} ttl - Time to live in seconds
   * @returns {Promise<boolean>} True if successful
   */
  async cacheData(key, data, ttl) {
    // Implementation will depend on whether MongoDB cache is enabled
    // For now, use enhancedCache
    return enhancedCache.set(key, data, ttl);
  }
  
  /**
   * Get service status
   * @returns {Object} Service status
   */
  getStatus() {
    return {
      stats: this.stats,
      providers: Object.keys(this.providers).reduce((acc, provider) => {
        const config = this.providers[provider];
        acc[provider] = {
          rateLimited: config.rateLimited,
          rateLimitResetTime: config.rateLimitResetTime,
          apiKeyCount: config.apiKeys.length,
          currentKeyIndex: config.currentKeyIndex
        };
        return acc;
      }, {}),
      retryQueue: this.retryQueue.getStatus()
    };
  }
}

export default EnhancedAPIService;
