import jwt from 'jsonwebtoken';
import { APIError } from './errorHandler.js';
import config from '../config/config.js';
import rateLimit from 'express-rate-limit';

export const protect = async (req, res, next) => {
  try {
    let token;

    // Get token from Authorization header
    if (req.headers.authorization?.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    // Check for token in cookies (for web clients)
    else if (req.cookies?.token) {
      token = req.cookies.token;
    }

    if (!token) {
      throw new APIError(401, 'Not authorized to access this route');
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, config.security.jwt.secret);

      // Add user from payload to request
      req.user = decoded;
      next();
    } catch (error) {
      throw new APIError(401, 'Not authorized to access this route');
    }
  } catch (error) {
    next(error);
  }
};

// Grant access to specific roles
export const authorize = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.role)) {
      throw new APIError(
        403,
        `User role ${req.user.role} is not authorized to access this route`
      );
    }
    next();
  };
};

// Rate limiting middleware
export const rateLimiter = rateLimit({
  windowMs: config.security.rateLimiter.windowMs,
  max: config.security.rateLimiter.max,
  message: 'Too many requests from this IP, please try again later',
});

export default {
  protect,
  authorize,
  rateLimiter,
}; 