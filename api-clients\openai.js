const axios = require('axios');
const logger = console;

/**
 * OpenAI API client
 * @param {Object} query - Query parameters
 * @param {string} query.type - Market type (analysis)
 * @param {string} query.symbol - Market symbol
 * @param {string} query.data - Additional data for analysis
 * @returns {Promise<Object>} - Analysis data
 */
module.exports = async function openai(query) {
  // Check if API key is available
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    throw new Error('OpenAI API key is not set');
  }

  try {
    logger.info(`Calling OpenAI API for analysis of ${query.symbol}`);
    
    // Prepare the market data context
    let marketContext = '';
    if (query.data && Array.isArray(query.data)) {
      marketContext = query.data.slice(0, 10).map(bar => 
        `Date: ${bar.timestamp}, Open: ${bar.open}, High: ${bar.high}, Low: ${bar.low}, Close: ${bar.close}, Volume: ${bar.volume}`
      ).join('\n');
    }
    
    // Construct the prompt for market analysis
    const prompt = `You are an expert financial analyst. Analyze the following market data for ${query.symbol} and provide insights:
    
Market data for ${query.symbol}:
${marketContext}

Please provide:
1. A summary of recent price action
2. Technical analysis with key levels and patterns
3. Trading recommendations (Buy, Sell, or Hold) with clear reasoning
4. Key risk factors to watch`;

    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: "gpt-3.5-turbo",
      messages: [
        { role: "system", content: "You are a financial market expert specialized in technical and fundamental analysis." },
        { role: "user", content: prompt }
      ],
      temperature: 0.3,
      max_tokens: 800
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 20000
    });

    if (!response.data || !response.data.choices || !response.data.choices[0]) {
      throw new Error('Invalid response format from OpenAI API');
    }

    return formatResponse(response.data, query);
  } catch (error) {
    logger.error('OpenAI API error:', error.message);
    if (error.response) {
      logger.error('Status:', error.response.status);
      logger.error('Data:', JSON.stringify(error.response.data));
    }
    throw error;
  }
};

/**
 * Format the OpenAI response to a standard format
 * @param {Object} data - OpenAI response
 * @param {Object} query - Original query
 * @returns {Object} - Formatted response
 */
function formatResponse(data, query) {
  const result = {
    symbol: query.symbol,
    type: query.type,
    source: 'openai',
    timestamp: new Date().toISOString()
  };

  // Extract the AI generated content
  const analysisText = data.choices[0].message.content;
  
  // Basic parsing of the analysis text
  const sections = analysisText.split(/\d\.\s+/).filter(Boolean);
  
  result.analysis = {
    raw: analysisText,
    summary: sections[0] || '',
    technical: sections[1] || '',
    recommendation: sections[2] || '',
    risks: sections[3] || '',
    tradingSignal: extractTradingSignal(analysisText)
  };

  return result;
}

/**
 * Extract trading signal from analysis text
 * @param {string} text - Analysis text
 * @returns {string} - Trading signal (BUY, SELL, HOLD)
 */
function extractTradingSignal(text) {
  // Look for explicit recommendations
  if (/\b(strongly\s+buy|buy\s+signal|recommended\s+buy)\b/i.test(text)) {
    return 'STRONG_BUY';
  } else if (/\b(strongly\s+sell|sell\s+signal|recommended\s+sell)\b/i.test(text)) {
    return 'STRONG_SELL';
  } else if (/\b(buy\b|buying\b|bullish\b)/i.test(text)) {
    return 'BUY';
  } else if (/\b(sell\b|selling\b|bearish\b)/i.test(text)) {
    return 'SELL';
  } else if (/\b(hold\b|neutral\b|waiting\b|wait\b)/i.test(text)) {
    return 'HOLD';
  }
  
  return 'NEUTRAL'; // Default if no clear signal found
}