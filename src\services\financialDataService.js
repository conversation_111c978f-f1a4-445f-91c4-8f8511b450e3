/**
 * Financial Data Service for Trading Signals App
 * 
 * This service integrates all the enhanced components:
 * - Enhanced API Service with retry queues
 * - MongoDB data storage
 * - WebSocket real-time updates
 * - Webhook integrations
 * - Dynamic parameter customization
 */

const EnhancedAPIService = require('./enhancedApiService');
const WebSocketService = require('./webSocketService');
const WebhookService = require('./webhookService');
const logger = require('../utils/logger');
const config = require('../config/config');
const { EventEmitter } = require('events');

class FinancialDataService extends EventEmitter {
  /**
   * Create a new FinancialDataService
   * @param {Object} options - Configuration options
   * @param {Object} options.db - MongoDB database instance
   * @param {Object} options.server - HTTP server instance for WebSocket
   */
  constructor(options = {}) {
    super();
    
    this.options = {
      enableWebSockets: true,
      enableWebhooks: true,
      enableDataStorage: true,
      ...options
    };
    
    // Initialize repositories
    this.repositories = {};
    if (options.db) {
      const EnhancedMarketDataRepository = require('../repositories/EnhancedMarketDataRepository');
      this.repositories.marketData = new EnhancedMarketDataRepository(options.db);
    }
    
    // Initialize API service
    this.apiService = new EnhancedAPIService();
    
    // Initialize WebSocket service if enabled
    if (this.options.enableWebSockets && options.server) {
      this.webSocketService = new WebSocketService(options.server);
      this.setupWebSocketEvents();
    }
    
    // Initialize Webhook service if enabled
    if (this.options.enableWebhooks) {
      this.webhookService = new WebhookService();
      this.setupWebhookEvents();
    }
    
    // Set up API service events
    this.setupAPIServiceEvents();
    
    // Initialize data refresh intervals
    this.refreshIntervals = new Map();
    
    logger.info('Financial Data Service initialized');
  }
  
  /**
   * Set up API service events
   */
  setupAPIServiceEvents() {
    this.apiService.on('data', (event) => {
      logger.debug(`Received data from API service: ${event.provider}/${event.endpoint}`);
      
      // Store data if storage is enabled
      if (this.options.enableDataStorage && this.repositories.marketData) {
        this.storeMarketData(event.data, event.provider, event.endpoint, event.params);
      }
      
      // Emit data event
      this.emit('data', event);
      
      // Publish to WebSocket if enabled
      if (this.options.enableWebSockets && this.webSocketService) {
        const channel = this.getChannelFromEndpoint(event.endpoint, event.params);
        this.webSocketService.publish(channel, event.data, event.params);
      }
      
      // Trigger webhooks if enabled
      if (this.options.enableWebhooks && this.webhookService) {
        const eventType = this.getEventTypeFromEndpoint(event.endpoint, event.params);
        this.webhookService.triggerEvent(eventType, {
          provider: event.provider,
          endpoint: event.endpoint,
          params: event.params,
          data: event.data
        });
      }
    });
    
    this.apiService.on('rateLimited', (event) => {
      logger.warn(`Provider ${event.provider} rate limited until ${new Date(event.resetTime).toISOString()}`);
      this.emit('rateLimited', event);
    });
    
    this.apiService.on('rateLimitReset', (event) => {
      logger.info(`Provider ${event.provider} rate limit reset`);
      this.emit('rateLimitReset', event);
    });
  }
  
  /**
   * Set up WebSocket events
   */
  setupWebSocketEvents() {
    // Handle client subscription requests
    this.webSocketService.on('subscribe', (event) => {
      logger.debug(`WebSocket client subscribed to ${event.channel}`);
      
      // Start data refresh for this subscription if needed
      this.startDataRefreshForChannel(event.channel, event.params);
    });
    
    // Handle client unsubscription
    this.webSocketService.on('unsubscribe', (event) => {
      logger.debug(`WebSocket client unsubscribed from ${event.channel}`);
      
      // Check if we still need to refresh this data
      this.checkAndStopDataRefresh(event.channel, event.params);
    });
  }
  
  /**
   * Set up Webhook events
   */
  setupWebhookEvents() {
    // Handle incoming webhooks
    this.webhookService.on('webhook', (event) => {
      logger.debug(`Received webhook for event ${event.event}`);
      
      // Process webhook data
      this.processWebhookData(event);
      
      // Emit webhook event
      this.emit('webhook', event);
    });
  }
  
  /**
   * Get channel name from endpoint and params
   * @param {string} endpoint - API endpoint
   * @param {Object} params - API parameters
   * @returns {string} Channel name
   */
  getChannelFromEndpoint(endpoint, params) {
    // Map API endpoints to WebSocket channels
    const endpointToChannel = {
      'TIME_SERIES_INTRADAY': 'market.data',
      'TIME_SERIES_DAILY': 'market.data',
      'GLOBAL_QUOTE': 'market.quote',
      'SMA': 'indicator.sma',
      'EMA': 'indicator.ema',
      'RSI': 'indicator.rsi',
      'MACD': 'indicator.macd',
      'BBANDS': 'indicator.bbands'
    };
    
    const channel = endpointToChannel[endpoint] || 'market.data';
    
    // Add symbol to channel if available
    if (params.symbol) {
      return `${channel}.${params.symbol.toLowerCase()}`;
    }
    
    return channel;
  }
  
  /**
   * Get event type from endpoint and params
   * @param {string} endpoint - API endpoint
   * @param {Object} params - API parameters
   * @returns {string} Event type
   */
  getEventTypeFromEndpoint(endpoint, params) {
    // Map API endpoints to webhook event types
    const endpointToEvent = {
      'TIME_SERIES_INTRADAY': 'market_data_update',
      'TIME_SERIES_DAILY': 'market_data_update',
      'GLOBAL_QUOTE': 'market_quote_update',
      'SMA': 'indicator_update',
      'EMA': 'indicator_update',
      'RSI': 'indicator_update',
      'MACD': 'indicator_update',
      'BBANDS': 'indicator_update'
    };
    
    return endpointToEvent[endpoint] || 'data_update';
  }
  
  /**
   * Store market data in MongoDB
   * @param {Object} data - Market data
   * @param {string} provider - API provider
   * @param {string} endpoint - API endpoint
   * @param {Object} params - API parameters
   */
  async storeMarketData(data, provider, endpoint, params) {
    try {
      if (!this.repositories.marketData) {
        return;
      }
      
      // Process data based on endpoint
      switch (endpoint) {
        case 'TIME_SERIES_INTRADAY':
        case 'TIME_SERIES_DAILY':
          await this.storeTimeSeriesData(data, provider, params);
          break;
        case 'GLOBAL_QUOTE':
          await this.storeQuoteData(data, provider, params);
          break;
        default:
          logger.debug(`No storage handler for endpoint ${endpoint}`);
      }
    } catch (error) {
      logger.error('Error storing market data:', error);
    }
  }
  
  /**
   * Store time series data
   * @param {Object} data - Time series data
   * @param {string} provider - API provider
   * @param {Object} params - API parameters
   */
  async storeTimeSeriesData(data, provider, params) {
    try {
      // Extract symbol and timeframe
      const symbol = params.symbol;
      const timeframe = params.interval || '1min';
      
      // Process data based on provider
      let dataPoints = [];
      
      if (provider === 'ALPHA_VANTAGE') {
        // Process Alpha Vantage data
        const timeSeries = data['Time Series (1min)'] || data['Time Series (5min)'] || 
                          data['Time Series (15min)'] || data['Time Series (30min)'] || 
                          data['Time Series (60min)'] || data['Time Series (Daily)'] || 
                          data['Time Series FX (1min)'] || data['Time Series Crypto (1min)'];
        
        if (!timeSeries) {
          logger.warn('No time series data found in Alpha Vantage response');
          return;
        }
        
        // Convert to data points
        dataPoints = Object.entries(timeSeries).map(([timestamp, values]) => ({
          symbol,
          timeframe,
          timestamp: new Date(timestamp),
          open: parseFloat(values['1. open']),
          high: parseFloat(values['2. high']),
          low: parseFloat(values['3. low']),
          close: parseFloat(values['4. close']),
          volume: parseFloat(values['5. volume'] || 0),
          source: provider.toLowerCase()
        }));
      } else if (provider === 'POLYGON') {
        // Process Polygon data
        if (!data.results || !Array.isArray(data.results)) {
          logger.warn('No results array found in Polygon response');
          return;
        }
        
        // Convert to data points
        dataPoints = data.results.map(item => ({
          symbol,
          timeframe,
          timestamp: new Date(item.t),
          open: item.o,
          high: item.h,
          low: item.l,
          close: item.c,
          volume: item.v,
          source: provider.toLowerCase()
        }));
      }
      
      // Bulk upsert data points
      if (dataPoints.length > 0) {
        await this.repositories.marketData.bulkUpsertMarketData(dataPoints);
        logger.info(`Stored ${dataPoints.length} data points for ${symbol}/${timeframe}`);
      }
    } catch (error) {
      logger.error('Error storing time series data:', error);
    }
  }
  
  /**
   * Store quote data
   * @param {Object} data - Quote data
   * @param {string} provider - API provider
   * @param {Object} params - API parameters
   */
  async storeQuoteData(data, provider, params) {
    try {
      // Extract symbol
      const symbol = params.symbol;
      
      // Process data based on provider
      let quoteData = null;
      
      if (provider === 'ALPHA_VANTAGE') {
        // Process Alpha Vantage data
        const quote = data['Global Quote'];
        
        if (!quote) {
          logger.warn('No quote data found in Alpha Vantage response');
          return;
        }
        
        quoteData = {
          symbol,
          timeframe: 'QUOTE',
          timestamp: new Date(),
          open: parseFloat(quote['02. open']),
          high: parseFloat(quote['03. high']),
          low: parseFloat(quote['04. low']),
          close: parseFloat(quote['05. price']),
          volume: parseFloat(quote['06. volume'] || 0),
          change: parseFloat(quote['09. change']),
          changePercent: parseFloat(quote['10. change percent'].replace('%', '')),
          source: provider.toLowerCase()
        };
      } else if (provider === 'POLYGON') {
        // Process Polygon data
        if (!data.ticker) {
          logger.warn('No ticker data found in Polygon response');
          return;
        }
        
        quoteData = {
          symbol,
          timeframe: 'QUOTE',
          timestamp: new Date(),
          open: data.open,
          high: data.high,
          low: data.low,
          close: data.close,
          volume: data.volume,
          change: data.todaysChange,
          changePercent: data.todaysChangePerc,
          source: provider.toLowerCase()
        };
      }
      
      // Upsert quote data
      if (quoteData) {
        await this.repositories.marketData.upsertMarketData(quoteData);
        logger.info(`Stored quote data for ${symbol}`);
      }
    } catch (error) {
      logger.error('Error storing quote data:', error);
    }
  }
  
  /**
   * Process webhook data
   * @param {Object} event - Webhook event
   */
  processWebhookData(event) {
    // Implementation depends on webhook data format
    logger.debug('Processing webhook data:', event);
    
    // Emit processed data
    this.emit('webhookProcessed', {
      event: event.event,
      data: event.data,
      processed: true,
      timestamp: new Date()
    });
  }
  
  /**
   * Start data refresh for a channel
   * @param {string} channel - Channel name
   * @param {Object} params - Channel parameters
   */
  startDataRefreshForChannel(channel, params) {
    // Check if we already have a refresh interval for this channel
    const key = `${channel}:${JSON.stringify(params)}`;
    
    if (this.refreshIntervals.has(key)) {
      return;
    }
    
    // Determine refresh interval based on channel
    let interval = 60000; // Default: 1 minute
    let fetchFunction = null;
    
    if (channel.startsWith('market.data')) {
      // Extract symbol from channel
      const symbol = channel.split('.')[2];
      
      if (symbol) {
        // Set up fetch function
        fetchFunction = async () => {
          await this.fetchMarketData(symbol, params.interval || '1min');
        };
        
        // Determine interval based on timeframe
        if (params.interval) {
          if (params.interval.includes('min')) {
            interval = 60000; // 1 minute
          } else if (params.interval.includes('hour')) {
            interval = 5 * 60000; // 5 minutes
          } else {
            interval = 30 * 60000; // 30 minutes
          }
        }
      }
    } else if (channel.startsWith('market.quote')) {
      // Extract symbol from channel
      const symbol = channel.split('.')[2];
      
      if (symbol) {
        // Set up fetch function
        fetchFunction = async () => {
          await this.fetchQuote(symbol);
        };
        
        interval = 30000; // 30 seconds for quotes
      }
    }
    
    // Start interval if we have a fetch function
    if (fetchFunction) {
      // Fetch immediately
      fetchFunction().catch(err => logger.error(`Error fetching data for ${channel}:`, err));
      
      // Set up interval
      const intervalId = setInterval(async () => {
        try {
          await fetchFunction();
        } catch (error) {
          logger.error(`Error refreshing data for ${channel}:`, error);
        }
      }, interval);
      
      // Store interval
      this.refreshIntervals.set(key, {
        intervalId,
        channel,
        params,
        interval,
        lastRefresh: Date.now()
      });
      
      logger.info(`Started data refresh for ${channel} every ${interval}ms`);
    }
  }
  
  /**
   * Check and stop data refresh if no subscribers
   * @param {string} channel - Channel name
   * @param {Object} params - Channel parameters
   */
  checkAndStopDataRefresh(channel, params) {
    // Only stop if WebSocket service is available
    if (!this.webSocketService) {
      return;
    }
    
    const key = `${channel}:${JSON.stringify(params)}`;
    
    // Check if we have an interval for this channel
    if (!this.refreshIntervals.has(key)) {
      return;
    }
    
    // Check if there are still subscribers
    const subscriptionKey = this.webSocketService.getSubscriptionKey(channel, params);
    const hasSubscribers = this.webSocketService.subscriptions.has(subscriptionKey) &&
                          this.webSocketService.subscriptions.get(subscriptionKey).size > 0;
    
    // If no subscribers, stop the interval
    if (!hasSubscribers) {
      const interval = this.refreshIntervals.get(key);
      clearInterval(interval.intervalId);
      this.refreshIntervals.delete(key);
      
      logger.info(`Stopped data refresh for ${channel} (no subscribers)`);
    }
  }
  
  /**
   * Fetch market data
   * @param {string} symbol - Trading symbol
   * @param {string} interval - Time interval
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Market data
   */
  async fetchMarketData(symbol, interval = '1min', options = {}) {
    try {
      // Determine provider and endpoint
      const provider = options.provider || 'ALPHA_VANTAGE';
      const endpoint = interval.includes('d') || interval.includes('day') ? 
                      'TIME_SERIES_DAILY' : 'TIME_SERIES_INTRADAY';
      
      // Prepare parameters
      const params = {
        symbol,
        interval,
        ...options
      };
      
      // Fetch data
      return await this.apiService.fetchWithRetryQueue({
        provider,
        endpoint,
        params,
        useCache: options.useCache !== false,
        cacheTTL: options.cacheTTL || 60 // 1 minute default TTL
      });
    } catch (error) {
      logger.error(`Error fetching market data for ${symbol}/${interval}:`, error);
      throw error;
    }
  }
  
  /**
   * Fetch quote data
   * @param {string} symbol - Trading symbol
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} Quote data
   */
  async fetchQuote(symbol, options = {}) {
    try {
      // Determine provider
      const provider = options.provider || 'ALPHA_VANTAGE';
      
      // Prepare parameters
      const params = {
        symbol,
        ...options
      };
      
      // Fetch data
      return await this.apiService.fetchWithRetryQueue({
        provider,
        endpoint: 'GLOBAL_QUOTE',
        params,
        useCache: options.useCache !== false,
        cacheTTL: 30 // 30 seconds default TTL for quotes
      });
    } catch (error) {
      logger.error(`Error fetching quote for ${symbol}:`, error);
      throw error;
    }
  }
}

module.exports = FinancialDataService;
