/**
 * MongoDB Service for Trading Signals App
 * 
 * This service provides a high-level interface for MongoDB operations.
 */

const { MongoClient, ServerApiVersion } = require('mongodb');
const repositoryFactory = require('../repositories/RepositoryFactory');
const logger = require('../utils/logger');

// MongoDB client
let client = null;
let db = null;

/**
 * Initialize MongoDB connection
 * @param {string} uri - MongoDB connection URI
 * @returns {Promise<Object>} MongoDB client and database
 */
async function initialize(uri) {
  try {
    logger.info('Initializing MongoDB connection');
    
    // Create MongoDB client
    client = new MongoClient(uri, {
      serverApi: {
        version: ServerApiVersion.v1,
        strict: true,
        deprecationErrors: true,
      }
    });
    
    // Connect to MongoDB
    await client.connect();
    logger.info('Connected to MongoDB successfully');
    
    // Ping the database to confirm connection
    await client.db('admin').command({ ping: 1 });
    logger.info('MongoDB ping successful - connection is fully working');
    
    // Get database
    db = client.db('tradingSignalsApp');
    
    // Initialize repository factory
    repositoryFactory.initialize(db);
    
    return { client, db };
  } catch (error) {
    logger.error('Error initializing MongoDB connection', error);
    throw error;
  }
}

/**
 * Get MongoDB database instance
 * @returns {Object} MongoDB database
 */
function getDatabase() {
  if (!db) {
    throw new Error('MongoDB not initialized');
  }
  return db;
}

/**
 * Get MongoDB client
 * @returns {Object} MongoDB client
 */
function getClient() {
  if (!client) {
    throw new Error('MongoDB not initialized');
  }
  return client;
}

/**
 * Close MongoDB connection
 * @returns {Promise<void>}
 */
async function close() {
  if (client) {
    await client.close();
    logger.info('MongoDB connection closed');
    client = null;
    db = null;
  }
}

/**
 * Create indexes for better performance
 * @returns {Promise<void>}
 */
async function createIndexes() {
  try {
    logger.info('Creating MongoDB indexes');
    
    // Users collection indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    
    // Trading signals collection indexes
    await db.collection('tradingSignals').createIndex({ symbol: 1 });
    await db.collection('tradingSignals').createIndex({ userId: 1 });
    await db.collection('tradingSignals').createIndex({ createdAt: -1 });
    await db.collection('tradingSignals').createIndex({ status: 1 });
    
    // Market data collection indexes
    await db.collection('marketData').createIndex(
      { symbol: 1, timeframe: 1, timestamp: -1 },
      { unique: true }
    );
    
    // User preferences collection indexes
    await db.collection('userPreferences').createIndex(
      { userId: 1 },
      { unique: true }
    );
    
    logger.info('MongoDB indexes created successfully');
  } catch (error) {
    logger.error('Error creating MongoDB indexes', error);
    throw error;
  }
}

/**
 * Check MongoDB connection status
 * @returns {Promise<Object>} Connection status
 */
async function checkStatus() {
  try {
    if (!client || !db) {
      return { status: 'disconnected', message: 'MongoDB not initialized' };
    }
    
    await client.db('admin').command({ ping: 1 });
    return { status: 'connected', message: 'MongoDB is connected and working properly' };
  } catch (error) {
    return { 
      status: 'error', 
      message: 'MongoDB connection error', 
      error: error.message 
    };
  }
}

module.exports = {
  initialize,
  getDatabase,
  getClient,
  close,
  createIndexes,
  checkStatus
};
