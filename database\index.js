const mongoose = require('mongoose');
const logger = require('../logging');

// Connection variables
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/trading-signals';
const MONGODB_OPTIONS = {
  useNewUrlParser: true,
  useUnifiedTopology: true,
  serverSelectionTimeoutMS: 5000,
  connectTimeoutMS: 10000,
};

// Track connection state
let isConnected = false;
let connectionAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 5;

/**
 * Connect to MongoDB
 * @returns {Promise<boolean>} Connection success
 */
async function connect() {
  // If already connected, return
  if (isConnected) {
    return true;
  }

  // Reset connection attempts if it's been a while
  if (connectionAttempts > 0 && Date.now() - lastAttemptTime > 60000) {
    connectionAttempts = 0;
  }

  // Track attempt time
  const lastAttemptTime = Date.now();
  
  // Increment attempts
  connectionAttempts++;
  
  try {
    // Set up event listeners for the connection
    mongoose.connection.on('connected', () => {
      isConnected = true;
      logger.info('Successfully connected to MongoDB');
    });
    
    mongoose.connection.on('error', (err) => {
      isConnected = false;
      logger.error(`MongoDB connection error: ${err.message}`);
    });
    
    mongoose.connection.on('disconnected', () => {
      isConnected = false;
      logger.warn('MongoDB disconnected');
    });
    
    // Connect to the database
    await mongoose.connect(MONGODB_URI, MONGODB_OPTIONS);
    isConnected = true;
    connectionAttempts = 0;
    
    logger.info(`Connected to MongoDB: ${MONGODB_URI.split('@').pop()}`);
    return true;
  } catch (error) {
    isConnected = false;
    const backoff = Math.min(connectionAttempts * 1000, 10000);
    
    logger.error(`MongoDB connection attempt ${connectionAttempts} failed: ${error.message}`);
    logger.info(`Will retry in ${backoff}ms`);
    
    if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
      setTimeout(() => connect(), backoff);
    } else {
      logger.error(`Max reconnection attempts (${MAX_RECONNECT_ATTEMPTS}) reached. Giving up.`);
    }
    
    return false;
  }
}

/**
 * Disconnect from MongoDB
 * @returns {Promise<boolean>} Disconnection success
 */
async function disconnect() {
  if (!isConnected) return true;
  
  try {
    await mongoose.disconnect();
    isConnected = false;
    logger.info('Disconnected from MongoDB');
    return true;
  } catch (error) {
    logger.error(`Error disconnecting from MongoDB: ${error.message}`);
    return false;
  }
}

/**
 * Check MongoDB connection status
 * @returns {Object} Health status object
 */
async function healthCheck() {
  try {
    if (!isConnected) {
      return { 
        status: 'disconnected',
        message: 'Not connected to MongoDB'
      };
    }
    
    // Check if connection is actually working with a ping
    const adminDb = mongoose.connection.db.admin();
    const result = await adminDb.ping();
    
    return {
      status: 'connected',
      ok: result.ok === 1,
      ping: result,
      dbName: mongoose.connection.name,
      dbHost: mongoose.connection.host,
      dbPort: mongoose.connection.port
    };
  } catch (error) {
    logger.error(`MongoDB health check failed: ${error.message}`);
    return {
      status: 'error',
      message: error.message
    };
  }
}

/**
 * Create a schema and model
 * @param {string} name - Model name
 * @param {Object} schemaDefinition - Mongoose schema definition
 * @param {Object} options - Additional options
 * @returns {Object} Mongoose model
 */
function createModel(name, schemaDefinition, options = {}) {
  const schema = new mongoose.Schema(schemaDefinition, {
    timestamps: true,
    ...options
  });
  
  // Add any schema methods, statics, or middleware here
  
  return mongoose.model(name, schema);
}

// Example models for the trading signals app

// Market data schema
const marketDataSchema = {
  symbol: { type: String, required: true, index: true },
  timeframe: { type: String, required: true, index: true },
  timestamp: { type: Date, required: true, index: true },
  open: { type: Number, required: true },
  high: { type: Number, required: true },
  low: { type: Number, required: true },
  close: { type: Number, required: true },
  volume: { type: Number, required: true },
  source: { type: String, required: true },
};

// Trading signal schema
const signalSchema = {
  symbol: { type: String, required: true, index: true },
  timeframe: { type: String, required: true, index: true },
  timestamp: { type: Date, required: true, index: true },
  signal: { type: String, required: true, enum: ['buy', 'sell', 'neutral'] },
  strength: { type: Number, required: true, min: 0, max: 100 },
  indicators: { type: Map, of: mongoose.Schema.Types.Mixed },
  price: { type: Number, required: true },
  metadata: { type: Map, of: mongoose.Schema.Types.Mixed },
};

// Create models
const MarketData = mongoose.models.MarketData || createModel('MarketData', marketDataSchema);
const Signal = mongoose.models.Signal || createModel('Signal', signalSchema);

// Connect when this module is imported
connect();

module.exports = {
  connect,
  disconnect,
  healthCheck,
  createModel,
  MarketData,
  Signal,
  mongoose
}; 