/**
 * MongoDB Connection Module for Trading Signals App
 */

const { MongoClient, ObjectId, ServerApiVersion } = require('mongodb');

// MongoDB Atlas Connection URI - Uses environment variable or default Atlas connection
// Replace <your_actual_password> with your MongoDB Atlas password in .env file
const uri = process.env.MONGODB_URI || "mongodb+srv://malaknagy03:${process.env.MONGODB_PASSWORD}@cluster0.au8xlc4.mongodb.net/?retryWrites=true&w=majority&appName=Cluster0";

// Create MongoDB client with Atlas options
const client = new MongoClient(uri, {
  serverApi: {
    version: ServerApiVersion.v1,
    strict: true,
    deprecationErrors: true,
  }
});

// Database name
const dbName = "tradingSignalsApp";

// Collections
const collections = {
  USERS: "users",
  SIGNALS: "tradingSignals",
  MARKET_DATA: "marketData",
  USER_PREFERENCES: "userPreferences"
};

// Connect to MongoDB
async function connect() {
  try {
    // Check if MongoDB password is set
    if (uri.includes('${process.env.MONGODB_PASSWORD}') && !process.env.MONGODB_PASSWORD) {
      console.error("MongoDB password not set in environment variables");
      throw new Error("MongoDB password not set. Please set MONGODB_PASSWORD in your .env file");
    }

    await client.connect();
    console.log("Connected to MongoDB successfully");

    // Verify connection with a ping
    await client.db("admin").command({ ping: 1 });
    console.log("MongoDB ping successful - connection is fully working");

    // Create indexes for better performance
    await createIndexes();

    return client.db(dbName);
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
    console.error("Please check your MongoDB connection string and credentials");
    throw error;
  }
}

// Get database instance
function getDb() {
  return client.db(dbName);
}

// Get a collection
function getCollection(collectionName) {
  return getDb().collection(collectionName);
}

// Close connection
async function close() {
  try {
    await client.close();
    console.log("MongoDB connection closed");
  } catch (error) {
    console.error("Error closing MongoDB connection:", error);
  }
}

// Create indexes for better performance
async function createIndexes() {
  try {
    // Users collection indexes
    const usersCollection = getCollection(collections.USERS);
    await usersCollection.createIndex({ email: 1 }, { unique: true });

    // Trading signals collection indexes
    const signalsCollection = getCollection(collections.SIGNALS);
    await signalsCollection.createIndex({ symbol: 1 });
    await signalsCollection.createIndex({ userId: 1 });
    await signalsCollection.createIndex({ createdAt: -1 });

    // Market data collection indexes
    const marketDataCollection = getCollection(collections.MARKET_DATA);
    await marketDataCollection.createIndex({ symbol: 1, timeframe: 1, timestamp: -1 });

    console.log("MongoDB indexes created successfully");
  } catch (error) {
    console.error("Error creating MongoDB indexes:", error);
  }
}

module.exports = {
  connect,
  getDb,
  getCollection,
  close,
  createIndexes,
  collections,
  ObjectId
};
