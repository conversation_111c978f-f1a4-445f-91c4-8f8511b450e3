import express from 'express';
import { protect } from '../middleware/auth.js';
import {
  register,
  login,
  getCurrentUser,
  updateUser,
  deleteUser,
  logout,
  setup2FA,
  verify2FA,
  disable2FA,
  exportUserData,
} from '../controllers/authController.js';

const router = express.Router();

// Public routes
router.post('/register', register);
router.post('/login', login);
router.post('/logout', logout);

// Protected routes
router.use(protect);
router.get('/me', getCurrentUser);
router.patch('/me', updateUser);
router.delete('/me', deleteUser);
router.post('/2fa/setup', setup2FA);
router.post('/2fa/verify', verify2FA);
router.post('/2fa/disable', disable2FA);
router.get('/export', exportUserData);

export default router; 