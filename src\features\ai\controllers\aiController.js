import { getAISignal } from '../services/aiService.js';

export const generateSignal = async (req, res, next) => {
  try {
    const { prompt } = req.body;
    if (!prompt) return res.status(400).json({ error: 'Prompt is required' });
    const signal = await getAISignal(prompt);
    res.json({ status: 'success', signal });
  } catch (error) {
    next(error);
  }
};

export default { generateSignal }; 