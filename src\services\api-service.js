/**
 * API Service for Trading Signals App
 * 
 * This file provides a unified API service for interacting with backend APIs.
 */

// API configuration
const API_CONFIG = {
  BASE_URL: '/api',
  ENDPOINTS: {
    MARKET_DATA: '/market-data',
    TECHNICAL_INDICATORS: '/technical-indicators',
    ECONOMIC_CALENDAR: '/economic-calendar',
    TRADING_SIGNALS: '/trading-signals',
    NEWS: '/market-news',
    STATUS: '/status'
  }
};

// Cache for API responses
const apiCache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// Fetch data from API
async function fetchFromAPI(endpoint, params = {}, options = {}) {
  try {
    // Create cache key from endpoint and params
    const cacheKey = JSON.stringify({ endpoint, params });
    
    // Check if data is in cache and not expired
    if (options.useCache !== false) {
      const cachedData = apiCache.get(cacheKey);
      if (cachedData && Date.now() - cachedData.timestamp < CACHE_TTL) {
        console.log('Using cached data for', endpoint);
        return cachedData.data;
      }
    }
    
    // Construct URL
    let url = API_CONFIG.BASE_URL + endpoint;
    
    // Add query parameters if any
    if (Object.keys(params).length > 0) {
      const queryParams = new URLSearchParams();
      Object.keys(params).forEach(key => {
        queryParams.append(key, params[key]);
      });
      url += '?' + queryParams.toString();
    }
    
    console.log('Fetching data from API:', url);
    
    // Fetch data
    const response = await fetch(url, {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      body: options.body ? JSON.stringify(options.body) : undefined
    });
    
    // Check if response is OK
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API error: ${response.status} ${response.statusText} - ${errorText}`);
    }
    
    // Parse response
    const data = await response.json();
    
    // Cache data if not a POST/PUT/DELETE request
    if (options.method === undefined || options.method === 'GET') {
      apiCache.set(cacheKey, {
        data,
        timestamp: Date.now()
      });
    }
    
    return data;
  } catch (error) {
    console.error('Error fetching from API:', error);
    
    // Show error message to user
    showErrorMessage(error.message);
    
    throw error;
  }
}

// Show error message to user
function showErrorMessage(message) {
  // Check if error container exists
  let errorContainer = document.getElementById('api-error-container');
  
  // Create error container if it doesn't exist
  if (!errorContainer) {
    errorContainer = document.createElement('div');
    errorContainer.id = 'api-error-container';
    errorContainer.style.position = 'fixed';
    errorContainer.style.top = '10px';
    errorContainer.style.right = '10px';
    errorContainer.style.zIndex = '9999';
    document.body.appendChild(errorContainer);
  }
  
  // Create error message element
  const errorElement = document.createElement('div');
  errorElement.className = 'api-error-message';
  errorElement.style.backgroundColor = 'rgba(255, 0, 0, 0.8)';
  errorElement.style.color = 'white';
  errorElement.style.padding = '10px';
  errorElement.style.borderRadius = '5px';
  errorElement.style.marginBottom = '5px';
  errorElement.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
  errorElement.textContent = message;
  
  // Add close button
  const closeButton = document.createElement('button');
  closeButton.textContent = '×';
  closeButton.style.marginLeft = '10px';
  closeButton.style.background = 'none';
  closeButton.style.border = 'none';
  closeButton.style.color = 'white';
  closeButton.style.fontSize = '16px';
  closeButton.style.cursor = 'pointer';
  closeButton.onclick = function() {
    errorContainer.removeChild(errorElement);
  };
  errorElement.appendChild(closeButton);
  
  // Add error message to container
  errorContainer.appendChild(errorElement);
  
  // Remove error message after 5 seconds
  setTimeout(() => {
    if (errorElement.parentNode === errorContainer) {
      errorContainer.removeChild(errorElement);
    }
  }, 5000);
}

// Get market data
async function getMarketData(symbol, interval = 'H1', assetType = null) {
  try {
    const params = { interval };
    if (assetType) {
      params.assetType = assetType;
    }
    
    return await fetchFromAPI(`${API_CONFIG.ENDPOINTS.MARKET_DATA}/${symbol}`, params);
  } catch (error) {
    console.error('Error getting market data:', error);
    return null;
  }
}

// Get technical indicators
async function getTechnicalIndicators(symbol) {
  try {
    return await fetchFromAPI(`${API_CONFIG.ENDPOINTS.TECHNICAL_INDICATORS}/${symbol}`);
  } catch (error) {
    console.error('Error getting technical indicators:', error);
    return null;
  }
}

// Get economic calendar
async function getEconomicCalendar() {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.ECONOMIC_CALENDAR);
  } catch (error) {
    console.error('Error getting economic calendar:', error);
    return null;
  }
}

// Get trading signals
async function getTradingSignals(symbol) {
  try {
    return await fetchFromAPI(`${API_CONFIG.ENDPOINTS.TRADING_SIGNALS}/${symbol}`);
  } catch (error) {
    console.error('Error getting trading signals:', error);
    return null;
  }
}

// Get market news
async function getMarketNews() {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.NEWS);
  } catch (error) {
    console.error('Error getting market news:', error);
    return null;
  }
}

// Check API status
async function checkAPIStatus() {
  try {
    return await fetchFromAPI(API_CONFIG.ENDPOINTS.STATUS, {}, { useCache: false });
  } catch (error) {
    console.error('Error checking API status:', error);
    return { status: 'error', message: error.message };
  }
}

// Export functions for external use
window.APIService = {
  getMarketData,
  getTechnicalIndicators,
  getEconomicCalendar,
  getTradingSignals,
  getMarketNews,
  checkAPIStatus,
  fetchFromAPI
};

console.log('API service loaded');
