/**
 * TradingView Chart Integration for Trading Signals App
 * 
 * This module provides integration with TradingView charts using the Lightweight Charts library.
 * It supports real-time data updates, multiple technical indicators, and interactive chart controls.
 * 
 * Features:
 * - Candlestick charts with multiple timeframes
 * - Technical indicators (SMA, EMA, RSI, MACD, Bollinger Bands)
 * - Real-time data updates via WebSocket
 * - Historical data loading with pagination
 * - Interactive chart controls (zoom, pan, crosshair)
 * - Multiple chart layouts (single, multi-pane)
 * - Theme support (light/dark mode)
 */

import { createChart } from 'lightweight-charts';
import { calculateSMA, calculateEMA, calculateRSI, calculateMACD, calculateBollingerBands } from './technical-indicators.js';
import unifiedAPIService from '../services/unified-api-service.js';

// Chart configuration defaults
const DEFAULT_CONFIG = {
  width: 800,
  height: 400,
  timeframe: '1h',
  symbol: 'BTCUSDT',
  indicators: ['sma', 'ema', 'volume'],
  indicatorSettings: {
    sma: { period: 20, color: 'rgba(4, 111, 232, 1)' },
    ema: { period: 21, color: 'rgba(255, 152, 0, 1)' },
    rsi: { period: 14, overbought: 70, oversold: 30, color: 'rgba(76, 175, 80, 1)' },
    macd: { 
      fast: 12, 
      slow: 26, 
      signal: 9, 
      macdColor: 'rgba(76, 175, 80, 1)',
      signalColor: 'rgba(255, 82, 82, 1)',
      histColor: 'rgba(187, 134, 252, 0.5)'
    },
    bollingerBands: { 
      period: 20, 
      stdDev: 2, 
      upperColor: 'rgba(76, 175, 80, 0.5)', 
      middleColor: 'rgba(255, 255, 255, 0.5)', 
      lowerColor: 'rgba(255, 82, 82, 0.5)' 
    },
    volume: { 
      upColor: 'rgba(76, 175, 80, 0.5)', 
      downColor: 'rgba(255, 82, 82, 0.5)' 
    }
  },
  theme: {
    chart: {
      layout: {
        backgroundColor: '#131722',
        textColor: '#d1d4dc',
      },
      watermark: {
        color: 'rgba(0, 0, 0, 0)',
      },
      crosshair: {
        color: '#758696',
      },
      grid: {
        vertLines: {
          color: '#2B2B43',
        },
        horzLines: {
          color: '#363C4E',
        },
      },
    },
    series: {
      topColor: 'rgba(38, 198, 218, 0.56)',
      bottomColor: 'rgba(38, 198, 218, 0.04)',
      lineColor: 'rgba(38, 198, 218, 1)',
    },
  }
};

/**
 * TradingViewChart class for managing chart instances
 */
class TradingViewChart {
  constructor(containerId, config = {}) {
    this.containerId = containerId;
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.container = document.getElementById(containerId);
    
    if (!this.container) {
      throw new Error(`Container element with ID "${containerId}" not found`);
    }
    
    this.charts = {};
    this.series = {};
    this.indicators = {};
    this.data = {
      ohlc: [],
      volume: [],
      indicators: {}
    };
    
    this.socket = null;
    this.isInitialized = false;
    this.lastUpdateTime = null;
    
    // Bind methods
    this.init = this.init.bind(this);
    this.updateData = this.updateData.bind(this);
    this.addIndicator = this.addIndicator.bind(this);
    this.removeIndicator = this.removeIndicator.bind(this);
    this.changeSymbol = this.changeSymbol.bind(this);
    this.changeTimeframe = this.changeTimeframe.bind(this);
    this.toggleIndicator = this.toggleIndicator.bind(this);
    this.applyTheme = this.applyTheme.bind(this);
    this.resize = this.resize.bind(this);
    this.destroy = this.destroy.bind(this);
    
    // Initialize the chart
    this.init();
  }
  
  /**
   * Initialize the chart
   */
  async init() {
    // Clear container
    this.container.innerHTML = '';
    
    // Create main chart
    this.charts.main = createChart(this.container, {
      width: this.config.width || this.container.clientWidth,
      height: this.config.height || 400,
      layout: this.config.theme.chart.layout,
      grid: this.config.theme.chart.grid,
      crosshair: {
        mode: 0,
        vertLine: {
          width: 1,
          color: this.config.theme.chart.crosshair.color,
          style: 1,
        },
        horzLine: {
          width: 1,
          color: this.config.theme.chart.crosshair.color,
          style: 1,
        },
      },
      timeScale: {
        timeVisible: true,
        secondsVisible: false,
        borderColor: this.config.theme.chart.grid.vertLines.color,
      },
      watermark: this.config.theme.chart.watermark,
    });
    
    // Create candlestick series
    this.series.candlestick = this.charts.main.addCandlestickSeries({
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });
    
    // Create volume series if enabled
    if (this.config.indicators.includes('volume')) {
      // Create a separate pane for volume
      this.charts.volume = this.charts.main.addHistogramSeries({
        color: '#26a69a',
        priceFormat: {
          type: 'volume',
        },
        priceScaleId: 'volume',
        scaleMargins: {
          top: 0.8,
          bottom: 0,
        },
      });
      
      this.series.volume = this.charts.volume;
    }
    
    // Load historical data
    try {
      await this.loadHistoricalData();
      
      // Add default indicators
      for (const indicator of this.config.indicators) {
        if (indicator !== 'volume') {
          this.addIndicator(indicator);
        }
      }
      
      // Connect to WebSocket for real-time updates
      this.connectWebSocket();
      
      this.isInitialized = true;
      console.log('TradingView chart initialized successfully');
      
      // Add event listeners
      window.addEventListener('resize', this.resize);
      
      // Dispatch event
      this.container.dispatchEvent(new CustomEvent('tvchartready', { detail: this }));
    } catch (error) {
      console.error('Failed to initialize TradingView chart:', error);
      this.container.innerHTML = `<div class="chart-error">Failed to load chart: ${error.message}</div>`;
    }
  }
  
  /**
   * Load historical data for the chart
   */
  async loadHistoricalData() {
    try {
      const response = await unifiedAPIService.fetchData({
        provider: 'ALPHA_VANTAGE',
        endpoint: 'TIME_SERIES_INTRADAY',
        params: {
          symbol: this.config.symbol,
          interval: this.config.timeframe,
          outputsize: 'full'
        },
        useCache: true
      });
      
      if (!response || !response.data) {
        throw new Error('No data received from API');
      }
      
      // Process the data
      this.processHistoricalData(response.data);
      
      // Update the chart
      this.updateChart();
      
      return true;
    } catch (error) {
      console.error('Error loading historical data:', error);
      throw error;
    }
  }
  
  /**
   * Process historical data from API response
   */
  processHistoricalData(data) {
    // Reset data arrays
    this.data.ohlc = [];
    this.data.volume = [];
    
    // Process data based on the API response format
    // This will need to be adjusted based on your actual API response structure
    if (Array.isArray(data)) {
      // Assuming data is an array of OHLCV objects
      this.data.ohlc = data.map(item => ({
        time: new Date(item.time).getTime() / 1000,
        open: parseFloat(item.open),
        high: parseFloat(item.high),
        low: parseFloat(item.low),
        close: parseFloat(item.close)
      }));
      
      this.data.volume = data.map(item => ({
        time: new Date(item.time).getTime() / 1000,
        value: parseFloat(item.volume),
        color: parseFloat(item.close) >= parseFloat(item.open) 
          ? this.config.indicatorSettings.volume.upColor 
          : this.config.indicatorSettings.volume.downColor
      }));
    } else if (data['Time Series']) {
      // Alpha Vantage format
      const timeSeriesKey = Object.keys(data).find(key => key.includes('Time Series'));
      const timeSeries = data[timeSeriesKey];
      
      this.data.ohlc = Object.entries(timeSeries).map(([time, values]) => ({
        time: new Date(time).getTime() / 1000,
        open: parseFloat(values['1. open']),
        high: parseFloat(values['2. high']),
        low: parseFloat(values['3. low']),
        close: parseFloat(values['4. close'])
      }));
      
      this.data.volume = Object.entries(timeSeries).map(([time, values]) => ({
        time: new Date(time).getTime() / 1000,
        value: parseFloat(values['5. volume']),
        color: parseFloat(values['4. close']) >= parseFloat(values['1. open']) 
          ? this.config.indicatorSettings.volume.upColor 
          : this.config.indicatorSettings.volume.downColor
      }));
    }
    
    // Sort data by time
    this.data.ohlc.sort((a, b) => a.time - b.time);
    this.data.volume.sort((a, b) => a.time - b.time);
    
    // Calculate indicators
    this.calculateIndicators();
  }
  
  /**
   * Calculate technical indicators based on price data
   */
  calculateIndicators() {
    const prices = this.data.ohlc.map(item => item.close);
    const times = this.data.ohlc.map(item => item.time);
    
    // Calculate SMA
    if (this.config.indicators.includes('sma')) {
      const period = this.config.indicatorSettings.sma.period;
      const smaValues = calculateSMA(prices, period);
      
      this.data.indicators.sma = times.map((time, index) => ({
        time,
        value: smaValues[index] || null
      }));
    }
    
    // Calculate EMA
    if (this.config.indicators.includes('ema')) {
      const period = this.config.indicatorSettings.ema.period;
      const emaValues = calculateEMA(prices, period);
      
      this.data.indicators.ema = times.map((time, index) => ({
        time,
        value: emaValues[index] || null
      }));
    }
    
    // Calculate RSI
    if (this.config.indicators.includes('rsi')) {
      const period = this.config.indicatorSettings.rsi.period;
      const rsiValues = calculateRSI(prices, period);
      
      this.data.indicators.rsi = times.map((time, index) => ({
        time,
        value: rsiValues[index] || null
      }));
    }
    
    // Calculate MACD
    if (this.config.indicators.includes('macd')) {
      const { fast, slow, signal } = this.config.indicatorSettings.macd;
      const macdResult = calculateMACD(prices, fast, slow, signal);
      
      this.data.indicators.macd = {
        macdLine: times.map((time, index) => ({
          time,
          value: macdResult.macdLine[index] || null
        })),
        signalLine: times.map((time, index) => ({
          time,
          value: macdResult.signalLine[index] || null
        })),
        histogram: times.map((time, index) => ({
          time,
          value: macdResult.histogram[index] || null,
          color: (macdResult.histogram[index] || 0) >= 0 
            ? 'rgba(76, 175, 80, 0.5)' 
            : 'rgba(255, 82, 82, 0.5)'
        }))
      };
    }
    
    // Calculate Bollinger Bands
    if (this.config.indicators.includes('bollingerBands')) {
      const { period, stdDev } = this.config.indicatorSettings.bollingerBands;
      const bbResult = calculateBollingerBands(prices, period, stdDev);
      
      this.data.indicators.bollingerBands = {
        upper: times.map((time, index) => ({
          time,
          value: bbResult.upper[index] || null
        })),
        middle: times.map((time, index) => ({
          time,
          value: bbResult.middle[index] || null
        })),
        lower: times.map((time, index) => ({
          time,
          value: bbResult.lower[index] || null
        }))
      };
    }
  }
  
  /**
   * Update the chart with the latest data
   */
  updateChart() {
    if (!this.isInitialized) return;
    
    // Update candlestick series
    if (this.series.candlestick && this.data.ohlc.length > 0) {
      this.series.candlestick.setData(this.data.ohlc);
    }
    
    // Update volume series
    if (this.series.volume && this.data.volume.length > 0) {
      this.series.volume.setData(this.data.volume);
    }
    
    // Update indicators
    this.updateIndicators();
    
    // Fit content if it's the first load
    if (!this.lastUpdateTime) {
      this.charts.main.timeScale().fitContent();
    }
    
    this.lastUpdateTime = new Date();
  }
  
  /**
   * Update indicator series with the latest data
   */
  updateIndicators() {
    // Update SMA
    if (this.indicators.sma && this.data.indicators.sma) {
      this.indicators.sma.setData(this.data.indicators.sma);
    }
    
    // Update EMA
    if (this.indicators.ema && this.data.indicators.ema) {
      this.indicators.ema.setData(this.data.indicators.ema);
    }
    
    // Update RSI
    if (this.indicators.rsi && this.data.indicators.rsi) {
      this.indicators.rsi.setData(this.data.indicators.rsi);
    }
    
    // Update MACD
    if (this.indicators.macd && this.data.indicators.macd) {
      this.indicators.macd.macdLine.setData(this.data.indicators.macd.macdLine);
      this.indicators.macd.signalLine.setData(this.data.indicators.macd.signalLine);
      this.indicators.macd.histogram.setData(this.data.indicators.macd.histogram);
    }
    
    // Update Bollinger Bands
    if (this.indicators.bollingerBands && this.data.indicators.bollingerBands) {
      this.indicators.bollingerBands.upper.setData(this.data.indicators.bollingerBands.upper);
      this.indicators.bollingerBands.middle.setData(this.data.indicators.bollingerBands.middle);
      this.indicators.bollingerBands.lower.setData(this.data.indicators.bollingerBands.lower);
    }
  }
  
  /**
   * Add a technical indicator to the chart
   */
  addIndicator(indicatorType) {
    if (!this.isInitialized) return;
    
    switch (indicatorType) {
      case 'sma':
        this.indicators.sma = this.charts.main.addLineSeries({
          color: this.config.indicatorSettings.sma.color,
          lineWidth: 2,
          priceLineVisible: false,
          lastValueVisible: true,
          title: `SMA (${this.config.indicatorSettings.sma.period})`,
        });
        
        if (this.data.indicators.sma) {
          this.indicators.sma.setData(this.data.indicators.sma);
        }
        break;
        
      case 'ema':
        this.indicators.ema = this.charts.main.addLineSeries({
          color: this.config.indicatorSettings.ema.color,
          lineWidth: 2,
          priceLineVisible: false,
          lastValueVisible: true,
          title: `EMA (${this.config.indicatorSettings.ema.period})`,
        });
        
        if (this.data.indicators.ema) {
          this.indicators.ema.setData(this.data.indicators.ema);
        }
        break;
        
      case 'rsi':
        // Create a separate pane for RSI
        const rsiPane = this.charts.main.addLineSeries({
          color: this.config.indicatorSettings.rsi.color,
          lineWidth: 2,
          priceLineVisible: false,
          lastValueVisible: true,
          title: `RSI (${this.config.indicatorSettings.rsi.period})`,
          priceScaleId: 'rsi',
          scaleMargins: {
            top: 0.8,
            bottom: 0,
          },
        });
        
        this.indicators.rsi = rsiPane;
        
        if (this.data.indicators.rsi) {
          this.indicators.rsi.setData(this.data.indicators.rsi);
        }
        break;
        
      // Additional indicators can be added here
        
      default:
        console.warn(`Indicator type "${indicatorType}" not supported`);
    }
  }
  
  /**
   * Remove an indicator from the chart
   */
  removeIndicator(indicatorType) {
    if (!this.isInitialized || !this.indicators[indicatorType]) return;
    
    // Remove the indicator series from the chart
    this.charts.main.removeSeries(this.indicators[indicatorType]);
    delete this.indicators[indicatorType];
    
    // Update the config
    const index = this.config.indicators.indexOf(indicatorType);
    if (index !== -1) {
      this.config.indicators.splice(index, 1);
    }
  }
  
  /**
   * Toggle an indicator on/off
   */
  toggleIndicator(indicatorType) {
    if (!this.isInitialized) return;
    
    if (this.indicators[indicatorType]) {
      this.removeIndicator(indicatorType);
    } else {
      this.addIndicator(indicatorType);
      this.config.indicators.push(indicatorType);
    }
  }
  
  /**
   * Change the trading symbol
   */
  async changeSymbol(symbol) {
    if (!this.isInitialized) return;
    
    this.config.symbol = symbol;
    
    // Disconnect WebSocket
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    // Load new data
    await this.loadHistoricalData();
    
    // Reconnect WebSocket
    this.connectWebSocket();
  }
  
  /**
   * Change the timeframe
   */
  async changeTimeframe(timeframe) {
    if (!this.isInitialized) return;
    
    this.config.timeframe = timeframe;
    
    // Disconnect WebSocket
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    // Load new data
    await this.loadHistoricalData();
    
    // Reconnect WebSocket
    this.connectWebSocket();
  }
  
  /**
   * Connect to WebSocket for real-time updates
   */
  connectWebSocket() {
    // Implementation depends on your WebSocket service
    console.log('Connecting to WebSocket for real-time updates...');
    
    // Example implementation using a custom WebSocket service
    // this.socket = new WebSocket(`wss://your-websocket-service.com/ws/${this.config.symbol}/${this.config.timeframe}`);
    
    // this.socket.onopen = () => {
    //   console.log('WebSocket connected');
    // };
    
    // this.socket.onmessage = (event) => {
    //   const data = JSON.parse(event.data);
    //   this.updateData(data);
    // };
    
    // this.socket.onerror = (error) => {
    //   console.error('WebSocket error:', error);
    // };
    
    // this.socket.onclose = () => {
    //   console.log('WebSocket disconnected');
    // };
  }
  
  /**
   * Update data with real-time information
   */
  updateData(newData) {
    if (!this.isInitialized) return;
    
    // Process new data
    const ohlcData = {
      time: newData.time,
      open: parseFloat(newData.open),
      high: parseFloat(newData.high),
      low: parseFloat(newData.low),
      close: parseFloat(newData.close)
    };
    
    const volumeData = {
      time: newData.time,
      value: parseFloat(newData.volume),
      color: parseFloat(newData.close) >= parseFloat(newData.open) 
        ? this.config.indicatorSettings.volume.upColor 
        : this.config.indicatorSettings.volume.downColor
    };
    
    // Update the last candle or add a new one
    const lastCandle = this.data.ohlc[this.data.ohlc.length - 1];
    
    if (lastCandle && lastCandle.time === ohlcData.time) {
      // Update existing candle
      lastCandle.high = Math.max(lastCandle.high, ohlcData.high);
      lastCandle.low = Math.min(lastCandle.low, ohlcData.low);
      lastCandle.close = ohlcData.close;
      
      // Update volume
      this.data.volume[this.data.volume.length - 1].value = volumeData.value;
      this.data.volume[this.data.volume.length - 1].color = volumeData.color;
    } else {
      // Add new candle
      this.data.ohlc.push(ohlcData);
      this.data.volume.push(volumeData);
    }
    
    // Recalculate indicators
    this.calculateIndicators();
    
    // Update the chart
    this.updateChart();
  }
  
  /**
   * Apply a theme to the chart
   */
  applyTheme(theme) {
    if (!this.isInitialized) return;
    
    this.config.theme = { ...this.config.theme, ...theme };
    
    // Apply theme to chart
    this.charts.main.applyOptions({
      layout: this.config.theme.chart.layout,
      grid: this.config.theme.chart.grid,
      crosshair: {
        vertLine: {
          color: this.config.theme.chart.crosshair.color,
        },
        horzLine: {
          color: this.config.theme.chart.crosshair.color,
        },
      },
      watermark: this.config.theme.chart.watermark,
    });
  }
  
  /**
   * Resize the chart
   */
  resize() {
    if (!this.isInitialized) return;
    
    const width = this.container.clientWidth;
    const height = this.config.height || 400;
    
    this.charts.main.resize(width, height);
  }
  
  /**
   * Destroy the chart instance and clean up
   */
  destroy() {
    if (!this.isInitialized) return;
    
    // Disconnect WebSocket
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    
    // Remove event listeners
    window.removeEventListener('resize', this.resize);
    
    // Destroy chart
    this.charts.main.remove();
    
    // Clear data
    this.data = {
      ohlc: [],
      volume: [],
      indicators: {}
    };
    
    this.isInitialized = false;
    console.log('TradingView chart destroyed');
  }
}

export default TradingViewChart;
