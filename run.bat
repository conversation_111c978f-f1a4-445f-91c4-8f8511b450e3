@echo off
SETLOCAL

REM Set the environment variable to development mode if not already set
IF "%NODE_ENV%"=="" SET NODE_ENV=development

REM Change to the project directory
CD /d "%~dp0"

ECHO =============================================
ECHO      Trading Signals App Startup
ECHO =============================================
ECHO Environment: %NODE_ENV%
ECHO Current Directory: %CD%
ECHO.

REM Check if Node.js is installed
WHERE node >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    ECHO ERROR: Node.js is not installed or not in PATH.
    ECHO Please install Node.js from https://nodejs.org/
    GOTO :EOF
)

ECHO Node.js version:
node --version
ECHO.

REM Check if required files exist
IF NOT EXIST "package.json" (
    ECHO ERROR: package.json not found.
    ECHO Please make sure you're in the right directory.
    GOTO :EOF
)

IF NOT EXIST ".env" (
    ECHO WARNING: .env file not found.
    ECHO Creating a sample .env file...
    ECHO PORT=3000 > .env
    ECHO MONGODB_URI=mongodb://localhost:27017/trading-signals >> .env
    ECHO ALPHA_VANTAGE_API_KEY=demo >> .env
    ECHO FINNHUB_API_KEY=demo >> .env
    ECHO TWELVE_DATA_API_KEY=demo >> .env
    ECHO FRED_API_KEY=demo >> .env
    ECHO JWT_SECRET=change_this_to_a_secure_random_string >> .env
)

REM Check for demo API keys
FINDSTR /C:"YOUR_API_KEY_HERE" /C:"demo" .env > nul
IF %ERRORLEVEL% EQU 0 (
    ECHO.
    ECHO *** WARNING: You are using demo API keys ***
    ECHO For full functionality, please edit the .env file and replace 
    ECHO the demo keys with your actual API keys from:
    ECHO.
    ECHO 1. Alpha Vantage: https://www.alphavantage.co/support/#api-key
    ECHO 2. Twelve Data: https://twelvedata.com/pricing
    ECHO 3. Finnhub: https://finnhub.io/register
    ECHO 4. FRED: https://fred.stlouisfed.org/docs/api/api_key.html
    ECHO.
    ECHO Press any key to continue anyway or Ctrl+C to cancel...
    PAUSE > NUL
)

REM Check if MongoDB is running
ECHO Checking if MongoDB is running...
WHERE mongod >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    ECHO WARNING: MongoDB executable not found. Using remote MongoDB connection.
) ELSE (
    netstat -an | findstr ":27017" > nul
    IF %ERRORLEVEL% NEQ 0 (
        ECHO Starting MongoDB...
        START "MongoDB" /B mongod --dbpath="data"
        TIMEOUT /T 5 /NOBREAK
    )
)

REM Check if Redis is running
ECHO Checking if Redis is running...
WHERE redis-server >nul 2>&1
IF %ERRORLEVEL% NEQ 0 (
    ECHO WARNING: Redis executable not found. App may not function correctly without Redis.
) ELSE (
    netstat -an | findstr ":6379" > nul
    IF %ERRORLEVEL% NEQ 0 (
        ECHO Starting Redis...
        START "Redis" /B redis-server
        TIMEOUT /T 3 /NOBREAK
    )
)

REM Check if node_modules exists, if not install dependencies
IF NOT EXIST "node_modules" (
    ECHO Installing dependencies...
    CALL npm install
    IF %ERRORLEVEL% NEQ 0 (
        ECHO ERROR: Failed to install dependencies.
        GOTO :EOF
    )
)

REM Build frontend assets if needed
IF EXIST "client" (
    ECHO Building frontend assets...
    CD client
    CALL npm install
    CALL npm run build
    CD ..
)

REM Start the application stack
ECHO Starting the application stack...
ECHO.

REM Start backend server using main.js instead of server.js
START "Trading Signals - Backend" cmd /k "node main.js"
TIMEOUT /T 3 /NOBREAK

REM Check if we need a simple static file server (if create-server.js exists)
IF EXIST "create-server.js" (
    IF NOT EXIST "public\index.html" (
        ECHO Starting simple static file server...
        START "Static File Server" cmd /k "node create-server.js"
    )
)

REM Start frontend development server if in development mode
IF "%NODE_ENV%"=="development" (
    IF EXIST "client" (
        START "Trading Signals - Frontend" cmd /k "cd client && npm start"
    )
)

ECHO.
ECHO =============================================
ECHO Trading Signals App is now running!
ECHO.
ECHO Backend API: http://localhost:3000
IF "%NODE_ENV%"=="development" (
    ECHO Frontend Dev Server: http://localhost:3001
) ELSE (
    ECHO Frontend: http://localhost:3000
)
ECHO.
ECHO MongoDB running on: localhost:27017 or using remote connection
ECHO Redis running on: localhost:6379 (if available)
ECHO =============================================
ECHO.
ECHO Press Ctrl+C in the respective windows to stop the services
ECHO Press any key to exit this launcher...
PAUSE > NUL

ENDLOCAL