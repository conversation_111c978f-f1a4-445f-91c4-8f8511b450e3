import { openaiAPI } from '../../../services/apiService.js';
import logger from '../../../utils/logger.js';

/**
 * Get AI-generated trading signal based on prompt
 * @param {string} prompt - User prompt for analysis
 * @returns {Promise<string>} - AI-generated response
 */
export async function getAISignal(prompt) {
  try {
    const response = await openaiAPI.post('chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      temperature: 0.4,
      max_tokens: 500,
    });
    
    return response.choices[0].message.content;
  } catch (error) {
    logger.error('Error getting AI signal:', error);
    throw new Error(`Failed to generate AI signal: ${error.message}`);
  }
}

/**
 * Generate technical analysis report
 * @param {Object} marketData - Market data including OHLCV
 * @param {Object} indicators - Technical indicators
 * @returns {Promise<Object>} - Analysis report
 */
export async function generateTechnicalAnalysisReport(marketData, indicators) {
  try {
    const prompt = `
      Generate a concise technical analysis report based on the following market data and indicators:
      
      Symbol: ${marketData.symbol}
      Timeframe: ${marketData.timeframe}
      Current Price: ${marketData.price}
      
      Technical Indicators:
      ${Object.entries(indicators).map(([name, value]) => `${name}: ${value}`).join('\n')}
      
      Provide:
      1. Market trend (bullish, bearish, or neutral)
      2. Key support and resistance levels
      3. Trading recommendation
    `;
    const report = await getAISignal(prompt);
    return report;
  } catch (error) {
    logger.error('Error generating technical analysis report:', error);
    throw new Error(`Failed to generate technical analysis report: ${error.message}`);
  }
}
