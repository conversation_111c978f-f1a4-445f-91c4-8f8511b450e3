import express from 'express';
import { protect } from '../middleware/auth.js';
import {
  getRealTimePrice,
  getHistoricalData,
  getMarketOverview,
  searchSymbols,
} from '../controllers/marketDataController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(protect);

// Market overview route
router.get('/overview', getMarketOverview);

// Symbol search route
router.get('/search', searchSymbols);

// Real-time price route
router.get('/price/:symbol', getRealTimePrice);

// Historical data route
router.get('/historical/:symbol', getHistoricalData);

export default router; 