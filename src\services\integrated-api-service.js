/**
 * Integrated API Service for Trading Signals App
 * 
 * This service combines multiple financial data APIs into a unified interface:
 * - Alpha Vantage
 * - FRED
 * - Polygon
 * - Finnhub
 * - Twelve Data
 * - FMP
 * 
 * Features:
 * - API key rotation
 * - Rate limit handling
 * - Response caching
 * - Error handling with fallbacks
 */

const axios = require('axios');
const path = require('path');
const fs = require('fs');

// Simple in-memory cache
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds

// API Keys (load from environment variables or use defaults)
const KEYS = {
  alpha: process.env.ALPHA_VANTAGE_API_KEY || 'OFU3DJH5JWW6Z29Z',
  polygon: process.env.POLYGON_API_KEY || 'YOUR_POLYGON_KEY',
  fred: process.env.FRED_API_KEY || 'de959b1589a8cd6ed0772b9127853054',
  twelve: process.env.TWELVE_DATA_API_KEY || 'YOUR_TWELVE_KEY',
  finnhub: process.env.FINNHUB_API_KEY || 'YOUR_FINNHUB_KEY',
  fmp: process.env.FMP_API_KEY || 'YOUR_FMP_KEY'
};

// API endpoints configuration
const ENDPOINTS = {
  alpha: {
    url: 'https://www.alphavantage.co/query',
    params: { function: 'TIME_SERIES_INTRADAY', symbol: 'AAPL', interval: '5min', apikey: KEYS.alpha }
  },
  polygon: {
    url: `https://api.polygon.io/v2/aggs/ticker/AAPL/prev`,
    params: { adjusted: 'true', apiKey: KEYS.polygon }
  },
  fred: {
    url: `https://api.stlouisfed.org/fred/series/observations`,
    params: { series_id: 'CPIAUCSL', api_key: KEYS.fred, file_type: 'json' }
  },
  twelve: {
    url: `https://api.twelvedata.com/time_series`,
    params: { symbol: 'AAPL', interval: '1min', apikey: KEYS.twelve }
  },
  finnhub: {
    url: `https://finnhub.io/api/v1/quote`,
    params: { symbol: 'AAPL', token: KEYS.finnhub }
  },
  fmp: {
    url: `https://financialmodelingprep.com/api/v3/quote/AAPL`,
    params: { apikey: KEYS.fmp }
  }
};

// Rate limit tracking
const rateLimits = {
  alpha: { limited: false, resetTime: null },
  polygon: { limited: false, resetTime: null },
  fred: { limited: false, resetTime: null },
  twelve: { limited: false, resetTime: null },
  finnhub: { limited: false, resetTime: null },
  fmp: { limited: false, resetTime: null }
};

/**
 * Generate a cache key from request parameters
 * @param {string} source - API source
 * @param {Object} params - Request parameters
 * @returns {string} Cache key
 */
function generateCacheKey(source, params) {
  return `${source}:${JSON.stringify(params)}`;
}

/**
 * Check if a response indicates rate limiting
 * @param {string} source - API source
 * @param {Object} data - Response data
 * @returns {boolean} True if rate limited
 */
function isRateLimited(source, data) {
  if (!data) return false;
  
  switch (source) {
    case 'alpha':
      return data.Note && data.Note.includes('API call frequency');
    case 'polygon':
      return data.status === 429 || (data.error && data.error.includes('rate limit'));
    case 'fred':
      return data.error_code === 429 || (data.error_message && data.error_message.includes('limit'));
    case 'finnhub':
      return data.error === 'API limit reached';
    default:
      return false;
  }
}

/**
 * Mark a source as rate limited
 * @param {string} source - API source
 * @param {number} resetTimeMs - Time in ms until rate limit resets
 */
function markRateLimited(source, resetTimeMs = 60000) {
  if (!rateLimits[source]) return;
  
  rateLimits[source].limited = true;
  rateLimits[source].resetTime = Date.now() + resetTimeMs;
  
  console.log(`[${source}] Rate limited until ${new Date(rateLimits[source].resetTime).toISOString()}`);
  
  // Auto-reset after timeout
  setTimeout(() => {
    if (rateLimits[source]) {
      rateLimits[source].limited = false;
      rateLimits[source].resetTime = null;
      console.log(`[${source}] Rate limit reset`);
    }
  }, resetTimeMs);
}

/**
 * Check if a source is currently rate limited
 * @param {string} source - API source
 * @returns {boolean} True if rate limited
 */
function isSourceRateLimited(source) {
  if (!rateLimits[source]) return false;
  
  if (!rateLimits[source].limited) return false;
  
  // Check if rate limit has expired
  if (rateLimits[source].resetTime && Date.now() > rateLimits[source].resetTime) {
    rateLimits[source].limited = false;
    rateLimits[source].resetTime = null;
    return false;
  }
  
  return true;
}

/**
 * Fetch data from a specific API source
 * @param {string} source - API source
 * @param {Object} params - Request parameters
 * @param {Object} options - Request options
 * @returns {Promise<Object>} Response data
 */
async function fetchData(source, params = {}, options = {}) {
  // Default options
  const defaultOptions = {
    useCache: true,
    cacheTTL: CACHE_TTL,
    timeout: 10000
  };
  
  const opts = { ...defaultOptions, ...options };
  
  // Check if source is valid
  if (!ENDPOINTS[source]) {
    throw new Error(`Invalid source: ${source}`);
  }
  
  // Check if source is rate limited
  if (isSourceRateLimited(source)) {
    throw new Error(`Source ${source} is currently rate limited`);
  }
  
  // Generate cache key
  const cacheKey = generateCacheKey(source, params);
  
  // Check cache if enabled
  if (opts.useCache) {
    const cachedData = cache.get(cacheKey);
    if (cachedData && cachedData.expiry > Date.now()) {
      console.log(`[${source}] Using cached data`);
      return cachedData.data;
    }
  }
  
  try {
    console.log(`[${source}] Fetching data...`);
    
    // Prepare request
    const endpoint = ENDPOINTS[source];
    const url = endpoint.url;
    const requestParams = { ...endpoint.params, ...params };
    
    // Make request
    const response = await axios.get(url, { 
      params: requestParams,
      timeout: opts.timeout
    });
    
    // Check for rate limiting
    if (isRateLimited(source, response.data)) {
      markRateLimited(source);
      throw new Error(`Rate limit reached for source ${source}`);
    }
    
    // Cache response if enabled
    if (opts.useCache) {
      cache.set(cacheKey, {
        data: response.data,
        expiry: Date.now() + opts.cacheTTL
      });
    }
    
    return response.data;
  } catch (error) {
    console.error(`[${source}] Fetch failed:`, error.response?.data || error.message);
    
    // Check if this is a rate limit error
    if (error.response && error.response.status === 429) {
      markRateLimited(source);
    }
    
    throw error;
  }
}

/**
 * Fetch data from all available sources
 * @param {Object} params - Request parameters
 * @param {Object} options - Request options
 * @returns {Promise<Object>} Results from all sources
 */
async function fetchAllData(params = {}, options = {}) {
  const results = {};
  const sources = ['alpha', 'fred']; // Only use Alpha Vantage and FRED by default
  
  // Add other sources if keys are configured
  if (KEYS.polygon !== 'YOUR_POLYGON_KEY') sources.push('polygon');
  if (KEYS.twelve !== 'YOUR_TWELVE_KEY') sources.push('twelve');
  if (KEYS.finnhub !== 'YOUR_FINNHUB_KEY') sources.push('finnhub');
  if (KEYS.fmp !== 'YOUR_FMP_KEY') sources.push('fmp');
  
  // Fetch data from all sources in parallel
  const promises = sources.map(async (source) => {
    try {
      const data = await fetchData(source, params, options);
      return { source, status: 'success', data };
    } catch (error) {
      return { 
        source, 
        status: 'error', 
        error: error.message,
        details: error.response?.data
      };
    }
  });
  
  const responses = await Promise.all(promises);
  
  // Organize results by source
  responses.forEach(response => {
    results[response.source] = response;
  });
  
  return results;
}

/**
 * Get service status
 * @returns {Object} Service status
 */
function getStatus() {
  return {
    sources: Object.keys(ENDPOINTS).map(source => ({
      name: source,
      enabled: true,
      rateLimited: isSourceRateLimited(source),
      resetTime: rateLimits[source]?.resetTime ? new Date(rateLimits[source].resetTime).toISOString() : null,
      hasValidKey: KEYS[source] && KEYS[source] !== `YOUR_${source.toUpperCase()}_KEY`
    })),
    cacheSize: cache.size,
    timestamp: new Date().toISOString()
  };
}

/**
 * Clear the cache
 */
function clearCache() {
  cache.clear();
  console.log('Cache cleared');
}

module.exports = {
  fetchData,
  fetchAllData,
  getStatus,
  clearCache,
  ENDPOINTS,
  KEYS
};
