const { MongoClient } = require('mongodb');
const mongoose = require('mongoose');
const logger = require('../logging');

let client = null;
let db = null;
let isConnecting = false;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const INITIAL_RECONNECT_DELAY = 1000;

/**
 * Connect to MongoDB database with exponential backoff
 * @returns {Promise<Object>} - MongoDB database instance
 */
async function connect() {
  if (!process.env.MONGODB_URI) {
    throw new Error('MONGODB_URI environment variable is not set');
  }

  if (isConnecting) {
    logger.info('MongoDB connection already in progress, waiting...');
    return new Promise((resolve) => {
      const checkConnection = setInterval(() => {
        if (db && !isConnecting) {
          clearInterval(checkConnection);
          resolve(db);
        }
      }, 100);
    });
  }

  try {
    // If we already have an active connection, use it
    if (client && client.topology && client.topology.isConnected()) {
      logger.debug('Using existing MongoDB connection');
      return client.db();
    }
    
    isConnecting = true;
    
    // Set mongoose connection options
    mongoose.set('strictQuery', false);
    
    // Connect to MongoDB using Mongoose for schema validation
    logger.info('Connecting to MongoDB...');
    
    // Use native MongoDB driver for connection
    client = new MongoClient(process.env.MONGODB_URI, {
      connectTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      serverSelectionTimeoutMS: 15000,
      maxPoolSize: 10,
      minPoolSize: 1,
      retryWrites: true,
      retryReads: true,
      w: 'majority'
    });

    await client.connect();
    db = client.db();
    
    // Monitor connection state
    client.on('serverClosed', () => {
      logger.warn('MongoDB server connection closed');
      reconnect();
    });
    
    client.on('error', (err) => {
      logger.error('MongoDB connection error:', err);
      reconnect();
    });

    // Reset reconnect attempts on successful connection
    reconnectAttempts = 0;
    isConnecting = false;
    logger.info('Successfully connected to MongoDB');
    
    return db;
  } catch (error) {
    isConnecting = false;
    logger.error('MongoDB connection error:', error);
    
    // Attempt reconnection
    await reconnect();
    throw error;
  }
}

/**
 * Attempt to reconnect to MongoDB with exponential backoff
 */
async function reconnect() {
  if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
    logger.error(`Maximum reconnection attempts (${MAX_RECONNECT_ATTEMPTS}) reached. Giving up.`);
    return;
  }

  reconnectAttempts++;
  
  // Calculate delay with exponential backoff
  const delay = INITIAL_RECONNECT_DELAY * Math.pow(2, reconnectAttempts - 1);
  logger.info(`Attempting to reconnect to MongoDB in ${delay}ms (attempt ${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`);
  
  setTimeout(async () => {
    try {
      await connect();
    } catch (error) {
      // Error handling is already in connect()
    }
  }, delay);
}

/**
 * Save a result to MongoDB
 * @param {string} collection - Collection name
 * @param {Object} data - Data to save
 * @returns {Promise<Object>} - MongoDB insertion result
 */
async function saveResult(collection, data) {
  if (!db) {
    await connect();
  }
  
  try {
    const result = await db.collection(collection).insertOne(data);
    logger.debug(`Saved document to ${collection} with ID: ${result.insertedId}`);
    return result;
  } catch (error) {
    logger.error(`Error saving to ${collection}:`, error);
    throw error;
  }
}

/**
 * Find results in MongoDB
 * @param {string} collection - Collection name
 * @param {Object} filter - MongoDB filter
 * @param {Object} options - MongoDB options
 * @returns {Promise<Array>} - Array of documents
 */
async function findResults(collection, filter = {}, options = {}) {
  if (!db) {
    await connect();
  }
  
  try {
    const results = await db.collection(collection).find(filter, options).toArray();
    logger.debug(`Found ${results.length} documents in ${collection}`);
    return results;
  } catch (error) {
    logger.error(`Error finding in ${collection}:`, error);
    throw error;
  }
}

/**
 * Update a document in MongoDB
 * @param {string} collection - Collection name
 * @param {Object} filter - MongoDB filter
 * @param {Object} update - Update operations
 * @param {Object} options - MongoDB options
 * @returns {Promise<Object>} - MongoDB update result
 */
async function updateDocument(collection, filter, update, options = {}) {
  if (!db) {
    await connect();
  }
  
  try {
    const result = await db.collection(collection).updateOne(filter, update, options);
    logger.debug(`Updated ${result.modifiedCount} document(s) in ${collection}`);
    return result;
  } catch (error) {
    logger.error(`Error updating in ${collection}:`, error);
    throw error;
  }
}

/**
 * Delete a document from MongoDB
 * @param {string} collection - Collection name
 * @param {Object} filter - MongoDB filter
 * @returns {Promise<Object>} - MongoDB deletion result
 */
async function deleteDocument(collection, filter) {
  if (!db) {
    await connect();
  }
  
  try {
    const result = await db.collection(collection).deleteOne(filter);
    logger.debug(`Deleted ${result.deletedCount} document(s) from ${collection}`);
    return result;
  } catch (error) {
    logger.error(`Error deleting from ${collection}:`, error);
    throw error;
  }
}

/**
 * Clean up MongoDB connection (for cleanup/testing)
 */
async function close() {
  if (client) {
    try {
      await client.close();
      client = null;
      db = null;
      logger.info('MongoDB connection closed');
    } catch (error) {
      logger.error('Error closing MongoDB connection:', error);
    }
  }
}

/**
 * Get MongoDB client instance
 * @returns {Object} - MongoDB client
 */
function getClient() {
  return client;
}

/**
 * Get MongoDB database instance
 * @returns {Object} - MongoDB database
 */
function getDb() {
  return db;
}

/**
 * Run MongoDB health check
 * @returns {Promise<Object>} - Health check result
 */
async function healthCheck() {
  try {
    if (!db) {
      await connect();
    }
    
    const result = await db.admin().ping();
    return {
      status: 'connected',
      ping: result.ok === 1,
      details: result
    };
  } catch (error) {
    logger.error('MongoDB health check failed:', error);
    return {
      status: 'disconnected',
      error: error.message
    };
  }
}

module.exports = {
  connect,
  saveResult,
  findResults,
  updateDocument,
  deleteDocument,
  close,
  getClient,
  getDb,
  healthCheck
}; 