import axios from 'axios';
import { getOpenAISentiment } from './openAISentimentService.js';
import SentimentHistory from '../models/SentimentHistory.js';
import COTHistory from '../models/COTHistory.js';

class NewsService {
  async getNews({ symbol }) {
    const apiKey = process.env.FMP_API_KEY;
    const url = `https://financialmodelingprep.com/api/v3/stock_news?tickers=${symbol}&limit=50&apikey=${apiKey}`;
    const { data } = await axios.get(url);
    return data;
  }

  async getSentiment({ symbol }) {
    const news = await this.getNews({ symbol });
    const headlines = news.map(n => n.title || n.headline);
    // Use OpenAI to analyze sentiment
    const sentiment = await getOpenAISentiment(headlines);
    // Save to history
    await saveSentimentHistory({ symbol, ...sentiment });
    return { symbol, ...sentiment };
  }

  async getCOT({ symbol }) {
    const apiKey = process.env.FMP_API_KEY;
    const url = `https://financialmodelingprep.com/api/v4/commitment_of_traders_report?symbol=${symbol}&apikey=${apiKey}`;
    const { data } = await axios.get(url);
    if (data && data.length > 0) {
      const latest = data[0];
      const cotData = {
        symbol: latest.symbol,
        reportDate: latest.date,
        commercials: { long: latest.commercial_long, short: latest.commercial_short },
        nonCommercials: { long: latest.non_commercial_long, short: latest.non_commercial_short },
        openInterest: latest.open_interest,
      };
      // Save to history
      await saveCOTHistory(cotData);
      return cotData;
    }
    return { symbol, error: 'No COT data found' };
  }
}

// Save sentiment after analysis
export async function saveSentimentHistory({ symbol, sentiment, score, summary }) {
  const date = new Date();
  await SentimentHistory.create({ symbol, date, sentiment, score, summary });
}

// Save COT after fetching
export async function saveCOTHistory({ symbol, reportDate, commercials, nonCommercials, openInterest }) {
  await COTHistory.create({ symbol, reportDate, commercials, nonCommercials, openInterest });
}

// Fetch sentiment history
export async function getSentimentHistory(symbol) {
  return SentimentHistory.find({ symbol }).sort({ date: 1 });
}

// Fetch COT history
export async function getCOTHistory(symbol) {
  return COTHistory.find({ symbol }).sort({ reportDate: 1 });
}

export default new NewsService(); 