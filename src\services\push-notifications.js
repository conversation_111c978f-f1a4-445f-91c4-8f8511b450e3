/**
 * Push Notifications Module for Trading Signals App
 * 
 * This file contains functionality for registering and sending push notifications
 * for important trading signals and market events.
 */

// Check if browser supports notifications
const notificationsSupported = 'Notification' in window;

// When the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize push notifications
    initializePushNotifications();
    
    // Add event listener for notification settings
    const notificationToggle = document.getElementById('notificationToggle');
    if (notificationToggle) {
        notificationToggle.addEventListener('change', function() {
            if (this.checked) {
                requestNotificationPermission();
            } else {
                disableNotifications();
            }
        });
    }
});

// Function to initialize push notifications
function initializePushNotifications() {
    // Check if notifications are supported
    if (!notificationsSupported) {
        console.warn('Push notifications are not supported by this browser');
        disableNotificationUI();
        return;
    }
    
    // Check if permission is already granted
    if (Notification.permission === 'granted') {
        enableNotificationUI();
    } else if (Notification.permission === 'denied') {
        disableNotificationUI();
    } else {
        // Default state (permission not asked yet)
        const notificationToggle = document.getElementById('notificationToggle');
        if (notificationToggle) {
            notificationToggle.checked = false;
        }
    }
}

// Function to request notification permission
function requestNotificationPermission() {
    if (!notificationsSupported) return;
    
    Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
            console.log('Notification permission granted');
            enableNotificationUI();
            
            // Send a test notification
            sendTestNotification();
        } else {
            console.warn('Notification permission denied');
            disableNotificationUI();
        }
    });
}

// Function to enable notification UI
function enableNotificationUI() {
    const notificationToggle = document.getElementById('notificationToggle');
    if (notificationToggle) {
        notificationToggle.checked = true;
    }
    
    const notificationStatus = document.getElementById('notificationStatus');
    if (notificationStatus) {
        notificationStatus.textContent = 'Enabled';
        notificationStatus.className = 'text-success';
    }
    
    // Save preference to localStorage
    localStorage.setItem('notifications_enabled', 'true');
}

// Function to disable notification UI
function disableNotificationUI() {
    const notificationToggle = document.getElementById('notificationToggle');
    if (notificationToggle) {
        notificationToggle.checked = false;
    }
    
    const notificationStatus = document.getElementById('notificationStatus');
    if (notificationStatus) {
        if (!notificationsSupported) {
            notificationStatus.textContent = 'Not Supported';
            notificationStatus.className = 'text-muted';
        } else {
            notificationStatus.textContent = 'Disabled';
            notificationStatus.className = 'text-danger';
        }
    }
    
    // Save preference to localStorage
    localStorage.setItem('notifications_enabled', 'false');
}

// Function to disable notifications
function disableNotifications() {
    disableNotificationUI();
}

// Function to send a test notification
function sendTestNotification() {
    if (Notification.permission === 'granted') {
        const notification = new Notification('Trading Signals App', {
            body: 'Notifications are now enabled. You will receive alerts for important trading signals.',
            icon: 'favicon.ico'
        });
        
        notification.onclick = function() {
            window.focus();
            notification.close();
        };
    }
}

// Function to send a trading signal notification
function sendTradingSignalNotification(signal) {
    if (Notification.permission !== 'granted' || !signal) return;
    
    const title = `${signal.type.toUpperCase()} Signal: ${signal.symbol}`;
    const body = `Entry: ${signal.entryPoint}, SL: ${signal.stopLoss}, TP: ${signal.takeProfit}`;
    
    const notification = new Notification(title, {
        body: body,
        icon: signal.type === 'buy' ? 'buy-icon.png' : 'sell-icon.png',
        badge: 'notification-badge.png',
        vibrate: [200, 100, 200]
    });
    
    notification.onclick = function() {
        window.focus();
        notification.close();
        
        // Scroll to signals section
        const signalsSection = document.getElementById('signalsCollapse');
        if (signalsSection) {
            signalsSection.scrollIntoView({ behavior: 'smooth' });
        }
    };
}

// Function to send a price alert notification
function sendPriceAlertNotification(symbol, price, condition) {
    if (Notification.permission !== 'granted') return;
    
    const title = `Price Alert: ${symbol}`;
    const body = `Price ${condition} ${price}`;
    
    const notification = new Notification(title, {
        body: body,
        icon: 'price-alert-icon.png',
        badge: 'notification-badge.png',
        vibrate: [200, 100, 200]
    });
    
    notification.onclick = function() {
        window.focus();
        notification.close();
        
        // Scroll to chart section
        const chartSection = document.getElementById('chartCollapse');
        if (chartSection) {
            chartSection.scrollIntoView({ behavior: 'smooth' });
        }
    };
}

// Function to send an economic event notification
function sendEconomicEventNotification(event) {
    if (Notification.permission !== 'granted' || !event) return;
    
    const title = `Economic Event: ${event.currency}`;
    const body = `${event.event} in ${getTimeUntilEvent(event.time)}`;
    
    const notification = new Notification(title, {
        body: body,
        icon: 'economic-event-icon.png',
        badge: 'notification-badge.png',
        vibrate: [200, 100, 200]
    });
    
    notification.onclick = function() {
        window.focus();
        notification.close();
        
        // Scroll to economic calendar section
        const calendarSection = document.getElementById('economicCalendarCollapse');
        if (calendarSection) {
            calendarSection.scrollIntoView({ behavior: 'smooth' });
        }
    };
}

// Helper function to get time until event
function getTimeUntilEvent(eventTime) {
    const now = new Date();
    const [hours, minutes] = eventTime.split(':').map(Number);
    
    const eventDate = new Date(now);
    eventDate.setHours(hours, minutes, 0, 0);
    
    // If event time is in the past for today, assume it's for tomorrow
    if (eventDate < now) {
        eventDate.setDate(eventDate.getDate() + 1);
    }
    
    const diffMs = eventDate - now;
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 60) {
        return `${diffMins} minutes`;
    } else {
        const hours = Math.floor(diffMins / 60);
        const mins = diffMins % 60;
        return `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? `and ${mins} minute${mins > 1 ? 's' : ''}` : ''}`;
    }
}

// Function to check for upcoming economic events and send notifications
function checkUpcomingEconomicEvents() {
    if (Notification.permission !== 'granted') return;
    
    // Get upcoming high-impact events
    const upcomingEvents = window.economicCalendar?.getUpcomingHighImpactEvents() || [];
    
    // Check if there are any events in the next 30 minutes
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    
    upcomingEvents.forEach(event => {
        const [hour, minute] = event.time.split(':').map(Number);
        
        // Calculate minutes until event
        let minutesUntil = (hour - currentHour) * 60 + (minute - currentMinute);
        if (minutesUntil < 0) {
            minutesUntil += 24 * 60; // Event is tomorrow
        }
        
        // Notify if event is within 30 minutes
        if (minutesUntil <= 30 && minutesUntil > 0) {
            sendEconomicEventNotification(event);
        }
    });
}

// Set up periodic checks for economic events
setInterval(checkUpcomingEconomicEvents, 5 * 60 * 1000); // Check every 5 minutes

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        initializePushNotifications,
        sendTradingSignalNotification,
        sendPriceAlertNotification,
        sendEconomicEventNotification
    };
} else {
    // For browser environment
    window.pushNotifications = {
        initializePushNotifications,
        sendTradingSignalNotification,
        sendPriceAlertNotification,
        sendEconomicEventNotification
    };
}
