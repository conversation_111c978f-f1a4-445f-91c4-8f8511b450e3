import axios from 'axios';

export async function getOpenAISentiment(headlines) {
  const apiKey = process.env.OPENAI_API_KEY;
  const prompt = `Analyze the following news headlines for overall market sentiment (bullish, bearish, or neutral) and provide a score from -1 (very bearish) to 1 (very bullish):\n\n${headlines.map((h, i) => `${i + 1}. ${h}`).join('\n')}\n\nRespond with a JSON object: { sentiment: "bullish|bearish|neutral", score: number, summary: "..." }`;

  const response = await axios.post(
    'https://api.openai.com/v1/chat/completions',
    {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
    },
    {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
    }
  );
  // Parse the JSON from the AI's response
  const text = response.data.choices[0].message.content;
  try {
    return JSON.parse(text);
  } catch {
    return { sentiment: 'neutral', score: 0, summary: 'Could not parse AI response.' };
  }
} 