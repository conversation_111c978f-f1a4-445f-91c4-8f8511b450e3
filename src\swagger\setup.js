/**
 * Swagger API Documentation Setup
 * 
 * This file configures Swagger UI for API documentation.
 */

const swaggerJsDoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const path = require('path');
const { version } = require('../../package.json');

/**
 * Set up Swagger documentation for the API
 * @param {Express} app - Express application
 */
function setupSwagger(app) {
  // Swagger configuration
  const swaggerOptions = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Trading Signals API',
        version,
        description: 'API for Trading Signals Application',
        license: {
          name: 'MIT',
          url: 'https://opensource.org/licenses/MIT',
        },
        contact: {
          name: 'API Support',
          email: '<EMAIL>',
        },
      },
      servers: [
        {
          url: '/api',
          description: 'Production server',
        },
        {
          url: 'http://localhost:3000/api',
          description: 'Development server',
        },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
        schemas: {
          Signal: {
            type: 'object',
            required: ['symbol', 'type', 'entryPrice', 'stopLoss', 'takeProfit', 'timeframe'],
            properties: {
              _id: {
                type: 'string',
                description: 'Signal ID (MongoDB ObjectId)',
                example: '60d21b4667d0d8992e610c85',
              },
              symbol: {
                type: 'string',
                description: 'Trading symbol',
                example: 'EURUSD',
              },
              type: {
                type: 'string',
                enum: ['buy', 'sell'],
                description: 'Signal type',
                example: 'buy',
              },
              entryPrice: {
                type: 'number',
                description: 'Entry price',
                example: 1.2345,
              },
              stopLoss: {
                type: 'number',
                description: 'Stop loss price',
                example: 1.2300,
              },
              takeProfit: {
                type: 'number',
                description: 'Take profit price',
                example: 1.2400,
              },
              timeframe: {
                type: 'string',
                enum: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1', 'W1', 'MN'],
                description: 'Signal timeframe',
                example: 'H1',
              },
              status: {
                type: 'string',
                enum: ['active', 'executed', 'expired'],
                description: 'Signal status',
                example: 'active',
              },
              analysis: {
                type: 'string',
                description: 'Signal analysis or notes',
                example: 'Strong bullish trend with support at 1.2300',
              },
              userId: {
                type: 'string',
                description: 'User ID (MongoDB ObjectId)',
                example: '60d21b4667d0d8992e610c85',
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                description: 'Signal creation date',
                example: '2023-01-01T12:00:00Z',
              },
              updatedAt: {
                type: 'string',
                format: 'date-time',
                description: 'Signal last update date',
                example: '2023-01-02T12:00:00Z',
              },
            },
          },
          User: {
            type: 'object',
            required: ['email', 'username', 'password'],
            properties: {
              _id: {
                type: 'string',
                description: 'User ID (MongoDB ObjectId)',
                example: '60d21b4667d0d8992e610c85',
              },
              email: {
                type: 'string',
                format: 'email',
                description: 'User email',
                example: '<EMAIL>',
              },
              username: {
                type: 'string',
                description: 'Username',
                example: 'johndoe',
              },
              password: {
                type: 'string',
                format: 'password',
                description: 'User password (hashed)',
                example: '$2a$10$...',
              },
              role: {
                type: 'string',
                enum: ['user', 'admin'],
                description: 'User role',
                example: 'user',
              },
              createdAt: {
                type: 'string',
                format: 'date-time',
                description: 'User creation date',
                example: '2023-01-01T12:00:00Z',
              },
              updatedAt: {
                type: 'string',
                format: 'date-time',
                description: 'User last update date',
                example: '2023-01-02T12:00:00Z',
              },
            },
          },
          Error: {
            type: 'object',
            properties: {
              status: {
                type: 'string',
                description: 'Error status',
                example: 'error',
              },
              message: {
                type: 'string',
                description: 'Error message',
                example: 'Invalid input data',
              },
              errors: {
                type: 'array',
                description: 'Validation errors',
                items: {
                  type: 'object',
                  properties: {
                    field: {
                      type: 'string',
                      description: 'Field with error',
                      example: 'email',
                    },
                    message: {
                      type: 'string',
                      description: 'Error message for field',
                      example: 'Email is required',
                    },
                  },
                },
                example: [
                  {
                    field: 'email',
                    message: 'Email is required',
                  },
                ],
              },
            },
          },
        },
      },
    },
    apis: [
      path.join(__dirname, '../server/routes/*.js'),
      path.join(__dirname, '../controllers/*.js'),
    ],
  };

  // Generate Swagger specification
  const swaggerSpec = swaggerJsDoc(swaggerOptions);

  // Serve Swagger documentation
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Trading Signals API Documentation',
  }));

  // Serve Swagger JSON
  app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
  });

  console.log('Swagger API documentation available at /api-docs');
}

module.exports = setupSwagger;
